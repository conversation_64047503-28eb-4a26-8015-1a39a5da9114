# Quick Integration Checklist

## ✅ Step-by-Step Integration

### 1. **Add Import to Each Page**
```jsx
import AnimatedBackground from '../Common/AnimatedBackground';
```

### 2. **For Each Section, Make These Changes:**

#### **Required Styling (CRITICAL):**
```jsx
// BEFORE:
<section style={{ backgroundColor: '#040a0f', padding: '60px 0' }}>

// AFTER:
<section style={{ 
  backgroundColor: '#040a0f', 
  padding: '60px 0',
  position: 'relative',  // ← ADD THIS
  overflow: 'hidden'     // ← ADD THIS
}}>
```

#### **Add AnimatedBackground:**
```jsx
<section style={{ position: 'relative', overflow: 'hidden' }}>
  {/* ADD THIS: */}
  <AnimatedBackground
    density="medium"
    colors={['rgba(0, 255, 200, 0.1)', 'rgba(99, 91, 255, 0.1)']}
    position="corner"
    speed="slow"
    shapes={['circle', 'square']}
  />
  
  {/* Your existing content */}
</section>
```

#### **Update Content Containers:**
```jsx
// BEFORE:
<div className="container">

// AFTER:
<div className="container" style={{ 
  position: 'relative',  // ← ADD THIS
  zIndex: 2              // ← ADD THIS
}}>
```

### 3. **Configuration by Section Type:**

#### **🎯 Hero Sections (Stripe-style):**
```jsx
<AnimatedBackground
  density="medium"
  colors={['rgba(0, 255, 200, 0.3)', 'rgba(99, 91, 255, 0.3)']}
  position="corner"      // ← Stripe-style corner
  speed="slow"
  shapes={['circle', 'square', 'diamond']}
/>
```

#### **🔵 Dark Blue Sections:**
```jsx
<AnimatedBackground
  density="medium"
  colors={['rgba(0, 255, 200, 0.1)', 'rgba(99, 91, 255, 0.1)']}
  position="full"
  speed="slow"
  shapes={['circle', 'square']}
/>
```

#### **🔷 Medium Blue Sections:**
```jsx
<AnimatedBackground
  density="medium"
  colors={['rgba(4, 10, 15, 0.1)', 'rgba(4, 10, 15, 0.15)']}
  position="center"
  speed="medium"
  shapes={['triangle', 'circle']}
/>
```

#### **📢 CTA Sections:**
```jsx
<AnimatedBackground
  density="low"          // ← Less distraction
  colors={['rgba(0, 255, 200, 0.08)', 'rgba(99, 91, 255, 0.08)']}
  position="full"
  speed="slow"
  shapes={['circle', 'square']}
/>
```

### 4. **Page-Specific Quick Fixes:**

#### **Homepage:**
- Hero video section: `position="corner"` (Stripe-style)
- CTA sections: `density="low"`
- Why Choose section: `density="medium"`

#### **Funds Page:**
- Intro section: `position="corner"`
- Content sections: `position="full"`
- Medium blue sections: dark colored shapes

#### **FAQ Page:**
- Alternate between `corner`, `center`, `full`
- Use educational shapes: `['circle', 'triangle']`
- Lower density for readability

### 5. **Test Checklist:**

- [ ] Import added to each page
- [ ] All sections have `position: 'relative'` and `overflow: 'hidden'`
- [ ] All content containers have `position: 'relative'` and `zIndex: 2`
- [ ] Animations are visible but subtle
- [ ] Text remains readable
- [ ] Mobile performance is smooth
- [ ] Animations respect `prefers-reduced-motion`

### 6. **Common Issues & Fixes:**

**❌ Animation not visible:**
- Check `position: 'relative'` on section
- Check `overflow: 'hidden'` on section

**❌ Content appears behind animation:**
- Add `position: 'relative', zIndex: 2` to content container

**❌ Animation too prominent:**
- Reduce opacity in colors: `rgba(0, 255, 200, 0.05)`
- Use `density="low"`

**❌ Performance issues:**
- Component automatically optimizes for mobile
- Reduce density if needed

### 7. **Ready-to-Copy Examples:**

See these files for complete examples:
- `HomepageIntegration.jsx` - Hero sections, CTA sections
- `FundsIntegration.jsx` - Product sections, alternating colors  
- `FAQIntegration.jsx` - Educational content, readability focus

### 8. **Final Result:**

✅ **Stripe-style corner animations** on hero sections  
✅ **Subtle floating shapes** on content sections  
✅ **Professional, non-distracting** visual enhancement  
✅ **Mobile-optimized** performance  
✅ **Accessible** (respects motion preferences)  

Your website will have the same sophisticated animated background as Stripe's homepage!
