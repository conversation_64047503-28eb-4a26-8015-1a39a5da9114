/* Global CSS Custom Properties for AlphaGlobal Website */
/* This file should be imported in index.css to make colors available globally */

:root {
  /* Background Colors */
  --dark-blue: #040a0f;
  --medium-blue: #f7f9fc;
  --white: #ffffff;
  
  /* Brand Colors */
  --red: #8B1E2D;                   /* Legacy red - kept for compatibility */
  --header-footer-red: #040a0f;     /* Dark blue for headers and footers */
  --button-red: #635bff; /* Orange for buttons and interactive elements */
  --button-text: #ffffff;           /* White text for buttons */
  --button-shadow: rgba(0, 0, 0, 0.6); /* strong neutral shadow for button hover */
  --card-color: #040a0f;            /* Color for cards and panels (dark blue) */
  --cta-background: transparent;    /* Transparent background for CTA sections */

  /* Frame Variables */
  --frame-color: #00ffc8;           /* Color for section frames */
  --frame-width: 60px;              /* Height/width of section frames */
  --frame2-color: #635bff;          /* Color for second frame type */
  --frame2-width: 60px;             /* Height/width of second frame type */
  
  /* Text Colors */
  --text-white: #ffffff;
  --text-black: #000000;
  --text-medium-blue: #000000;          /* Black text for cyan backgrounds */
  
  /* Utility Colors */
  --success-green: #3ed68c;
  --warning-yellow: #ffd93d;
  --error-red: #ff6b6b;
  --info-blue: #4BC0C0;
  
  /* Chart Colors */
  --chart-performance: #10b981;
  --chart-risk: #040a0f;
  --chart-fees: #3b82f6;
  --chart-liquidity: #8b5cf6;
  
  /* Transparency Variants */
  --white-10: rgba(255, 255, 255, 0.1);
  --white-20: rgba(255, 255, 255, 0.2);
  --white-30: rgba(255, 255, 255, 0.3);
  --black-10: rgba(0, 0, 0, 0.1);
  --black-20: rgba(0, 0, 0, 0.2);
  --black-30: rgba(0, 0, 0, 0.3);
}

/* Global rules to ensure white text on blue backgrounds */
/* Any element with dark blue background must have white text */
[style*="background-color: #040a0f"],
[style*="backgroundColor: #040a0f"],
[style*="background-color: var(--dark-blue)"],
[style*="backgroundColor: var(--dark-blue)"],
.dark-blue-bg {
  color: white !important;
}

[style*="background-color: #040a0f"] *,
[style*="backgroundColor: #040a0f"] *,
[style*="background-color: var(--dark-blue)"] *,
[style*="backgroundColor: var(--dark-blue)"] *,
.dark-blue-bg * {
  color: white !important;
}

/* Any element with medium blue background must have black text */
[style*="background-color: #00ffc8"],
[style*="backgroundColor: #00ffc8"],
[style*="background-color: var(--medium-blue)"],
[style*="backgroundColor: var(--medium-blue)"],
.medium-blue-bg {
  color: var(--text-medium-blue) !important;
}

[style*="background-color: #00ffc8"] *,
[style*="backgroundColor: #00ffc8"] *,
[style*="background-color: var(--medium-blue)"] *,
[style*="backgroundColor: var(--medium-blue)"] *,
.medium-blue-bg * {
  color: var(--text-medium-blue) !important;
}

/* Utility classes for forcing white text */
.force-white-text,
.force-white-text * {
  color: white !important;
}

/* Override any other color rules for blue backgrounds */
.dark-blue-bg h1, .dark-blue-bg h2, .dark-blue-bg h3, .dark-blue-bg h4, .dark-blue-bg h5, .dark-blue-bg h6,
.medium-blue-bg h1, .medium-blue-bg h2, .medium-blue-bg h3, .medium-blue-bg h4, .medium-blue-bg h5, .medium-blue-bg h6 {
  color: white !important;
}

.dark-blue-bg p, .dark-blue-bg span, .dark-blue-bg div, .dark-blue-bg td, .dark-blue-bg th, .dark-blue-bg li,
.medium-blue-bg p, .medium-blue-bg span, .medium-blue-bg div, .medium-blue-bg td, .medium-blue-bg th, .medium-blue-bg li {
  color: white !important;
}

/* Override any CSS that might set dark text colors */
.fund-content, .fund-content *,
.fund-overview-content, .fund-overview-content *,
.performance-container, .performance-container *,
.documents-container, .documents-container *,
.whatif-container, .whatif-container * {
  color: white !important;
}

/* Specific overrides for common dark text classes */
.summary-value, .summary-box h3, .summary-box p,
.document-title h3, .document-title p,
.risk-profile, .risk-profile *,
.fund-header, .fund-meta, .meta-item {
  color: white !important;
}
