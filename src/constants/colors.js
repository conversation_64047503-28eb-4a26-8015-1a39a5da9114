// Global Color Palette for AlphaGlobal Website
// This file controls all colors used throughout the site

export const COLORS = {
  // Background Colors
  DARK_BLUE: '#040a0f',      // Darkest blue for main backgrounds
  MEDIUM_BLUE: '#f7f9fc',    // Bright cyan for cards and sections
  WHITE: '#ffffff',          // White backgrounds
  
  // Brand Colors
  RED: '#8B1E2D',                    // Legacy red - kept for compatibility
  HEADER_FOOTER_RED: '#040a0f',      // Dark blue for headers and footers
  BUTTON_RED: '#635bff', // Orange for buttons and interactive elements
  BUTTON_TEXT: '#ffffff',            // White text for buttons
  BUTTON_SHADOW: 'rgba(0, 0, 0, 0.6)', // Strong neutral shadow for button hover
  CARD_COLOR: '#040a0f',             // Color for cards and panels (dark blue)
  CTA_BACKGROUND: 'transparent',     // Transparent background for CTA sections

  // Frame Colors
  FRAME_COLOR: '#00ffc8',            // Color for section frames
  FRAME_WIDTH: '60px',               // Height/width of section frames
  FRAME2_COLOR: '#635bff',           // Color for second frame type
  FRAME2_WIDTH: '60px',              // Height/width of second frame type
  
  // Text Colors
  TEXT_WHITE: '#ffffff',     // White text
  TEXT_BLACK: '#000000',     // Black text
  TEXT_MEDIUM_BLUE: '#000000', // Black text for medium blue backgrounds
  
  // Utility Colors (for charts, status indicators, etc.)
  SUCCESS_GREEN: '#3ed68c',  // Positive performance, success states
  WARNING_YELLOW: '#ffd93d', // Medium risk, warnings
  ERROR_RED: '#ff6b6b',      // Negative performance, errors
  INFO_BLUE: '#4BC0C0',      // Information, neutral states

  // Summary Chart Colors (Homepage)
  CHART_PERFORMANCE: '#10b981',  // Green for Performance bars
  CHART_RISK: '#040a0f',         // Dark blue for Risk Level bars
  CHART_FEES: '#3b82f6',         // Blue for Low Fees bars
  CHART_LIQUIDITY: '#8b5cf6',    // Purple for Liquidity bars
  
  // Transparency Variants (for borders, overlays, etc.)
  WHITE_10: 'rgba(255, 255, 255, 0.1)',
  WHITE_20: 'rgba(255, 255, 255, 0.2)',
  WHITE_30: 'rgba(255, 255, 255, 0.3)',
  BLACK_10: 'rgba(0, 0, 0, 0.1)',
  BLACK_20: 'rgba(0, 0, 0, 0.2)',
  BLACK_30: 'rgba(0, 0, 0, 0.3)',
};

// Semantic color mappings for specific use cases
export const SEMANTIC_COLORS = {
  // Page backgrounds
  PAGE_BACKGROUND: COLORS.MEDIUM_BLUE,
  
  // Card and section backgrounds
  CARD_BACKGROUND: COLORS.DARK_BLUE,
  SECTION_BACKGROUND: COLORS.MEDIUM_BLUE,
  
  // Text colors
  PRIMARY_TEXT: COLORS.TEXT_WHITE,
  SECONDARY_TEXT: COLORS.TEXT_BLACK,
  
  // Brand elements
  BRAND_PRIMARY: COLORS.RED,                    // Legacy - kept for compatibility
  BRAND_ACCENT: COLORS.DARK_BLUE,
  HEADER_FOOTER_COLOR: COLORS.HEADER_FOOTER_RED, // Specific color for headers and footers
  BUTTON_COLOR: COLORS.BUTTON_RED,               // Specific color for buttons
  CARD_COLOR: COLORS.CARD_COLOR,                 // Specific color for cards and panels
  CTA_BACKGROUND_COLOR: COLORS.CTA_BACKGROUND,   // Specific color for CTA backgrounds
  
  // Interactive elements
  BORDER_SUBTLE: COLORS.WHITE_10,
  BORDER_VISIBLE: COLORS.WHITE_20,
  OVERLAY_LIGHT: COLORS.WHITE_10,
  
  // Performance indicators
  PERFORMANCE_POSITIVE: COLORS.SUCCESS_GREEN,
  PERFORMANCE_NEGATIVE: COLORS.ERROR_RED,
  RISK_LOW: COLORS.SUCCESS_GREEN,
  RISK_MEDIUM: COLORS.WARNING_YELLOW,
  RISK_HIGH: COLORS.RED,

  // Summary Chart Colors (Homepage)
  CHART_PERFORMANCE_BAR: COLORS.CHART_PERFORMANCE,
  CHART_RISK_BAR: COLORS.CHART_RISK,
  CHART_FEES_BAR: COLORS.CHART_FEES,
  CHART_LIQUIDITY_BAR: COLORS.CHART_LIQUIDITY,
};

export default COLORS;
