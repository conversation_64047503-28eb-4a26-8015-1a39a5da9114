import React, { useState } from 'react';
import CTASection from '../../App/CTA/CTASection.jsx';
import FullHeightHero from '../Common/FullHeightHero';
import SectionFrame from '../Common/SectionFrame';
import SectionFrame2 from '../Common/SectionFrame2';

// Import global colors
import { COLORS } from '../../constants/colors';

// Use global colors directly
const STRATEGIES_COLORS = COLORS;

const Strategies = () => {
  const [activeIndex, setActiveIndex] = useState(null);

  const risks = [
    {
      question: 'Market Risk',
      definition: 'Exposure to losses from market prices fluctuations. Crypto assets are highly volatile, leading to significant price swings.',
      management: 'Use Value-at-Risk (VaR) models estimate potential losses over a set period. Strict control of leverage is critical to minimize losses.',
      mitigation: 'Use of hedging strategies (options, futures), portfolio diversification, and setting stop-loss limits.'
    },
    {
      question: 'Liquidity Risk',
      definition: 'Inability to meet obligations for payments and redemptions due to less than perfectly liquid assets.',
      management: 'Measure and use liquidity metrics such as Liquidity Coverage Ratio(LCR) used in traditional finance.',
      mitigation: 'Balance liquid reserves with illiquid investments. Align term structure assets and liabilities. Structure redemption terms and apply fees for requests to redeem funds at short notice.'
    },
    {
      question: 'Credit Risk',
      definition: 'Exposure to third parties such as exchanges, custodians, or counterparties in derivatives trading.',
      management: 'Managed exposures with strict alignment to fund objectives and liquidity requirements via ALM. Verify and risk test counterparties. Measure and manage concentration risks by coin/fund/regulatory regime.',
      mitigation: 'Conducting thorough due diligence, diversifying counterparties, and using smart contracts with decentralized finance (DeFi) protocols cautiously.'
    },
    {
      question: 'Operational Risk',
      definition: 'Inefficiencies in trading execution, order processing, and compliance failures e.g., internal failures like systems, fraud, cyberattacks.',
      management: 'Robust IT infrastructure. Continuous education and training of staff. Automated compliance tools to ensure practice follows policy. Applied & verified cybersecurity protocols.',
      mitigation: 'Automating processes where possible, implementing robust risk management frameworks, and conducting regular stress tests.'
    },
    {
      question: 'Legal Risk',
      definition: 'Penalties from non-compliance e.g. MICA regulations(Europe), AML/KYC (multiple locations). Regulatory Uncertainty – Evolving regulations across different jurisdictions can impact fund operations.',
      management: 'On-call legal teams, applied regulatory technology (RegTech) and jurisdictional diversification.',
      mitigation: 'Legal and compliance teams tracking legal updates, structuring funds in crypto-friendly jurisdictions, and maintaining flexible investment strategies.'
    },
    {
      question: 'Regulatory Risk',
      definition: 'Regulatory risk is the threat of adverse changes in laws or regulations impacting a crypto fund\'s operations, legality, or profitability.',
      management: 'Moolah capital can manage regulatory risk by monitoring legal developments, ensuring compliance, engaging legal counsel, and diversifying across jurisdictions.',
      mitigation: 'Crypto funds can\'t fully mitigate regulatory risk, but proactive compliance, legal counsel, and jurisdictional diversification help manage potential impacts.'
    }
  ];

  const toggleAccordion = index => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const renderAccordion = (risks, activeIndex, toggleAccordion) => (
    <div className="accordion" id="accordionFlushExample">
      {risks.map((risk, index) => (
        <div className="accordion-item" key={index} style={{ backgroundColor: STRATEGIES_COLORS.DARK_BLUE, border: 'none' }}>
          <h2 className="accordion-header" id={`flush-heading${index}`}>
            <button
              className={`accordion-button ${activeIndex === index ? '' : 'collapsed'}`}
              type="button"
              onClick={() => toggleAccordion(index)}
              aria-expanded={activeIndex === index}
              aria-controls={`flush-collapse${index}`}
              style={{
                backgroundColor: STRATEGIES_COLORS.DARK_BLUE,
                color: 'white',
                border: 'none',
                boxShadow: 'none'
              }}
            >
              {risk.question}
            </button>
          </h2>
          <div
            id={`flush-collapse${index}`}
            className={`accordion-collapse collapse ${activeIndex === index ? 'show' : ''}`}
            aria-labelledby={`flush-heading${index}`}
          >
            <div className="accordion-body" style={{ backgroundColor: STRATEGIES_COLORS.DARK_BLUE, color: 'white' }}>
              <p><strong>Definition:</strong> {risk.definition}</p>
              <p><strong>Management:</strong> {risk.management}</p>
              <p><strong>Mitigation:</strong> {risk.mitigation}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const cardStrategyList = [
    { id: 1, title: 'Applied Intelligence', helpText: 'Rapidly Evolving AI Models', img: '/strategies-ai.jpg', description: [
        '<p>We use advanced analytics, machine learning, and algorithmic strategies to manage portfolios from start to finish — including asset selection, rebalancing, and risk control.</p>',
        '<p>Our systems process market, sentiment, and macro data, then apply AI models like GPT-4, Claude, Meta.ai, and DeepSeek to automate research, test ideas, validate trades, and generate insights.</p>',
        '<p>This approach helps us stay ahead of market trends and make data-driven decisions at scale.</p>'
      ]
    },
    { id: 2, title: 'DeFi Integration', helpText: 'Decentralised Finance', img: '/strategies-crypto-market.jpg', description: [
        '<p>We leverage decentralized finance protocols to access new yield opportunities, liquidity pools, and innovative financial instruments.</p>',
        '<p>Our DeFi strategies include yield farming, liquidity provision, and participation in governance tokens, all while maintaining strict risk management protocols.</p>',
        '<p>By integrating DeFi into our traditional investment approach, we offer clients exposure to the next generation of financial infrastructure.</p>'
      ]
    },
    { id: 3, title: 'RWA Tokenisation', helpText: 'Real World Assets', img: '/strategies-diversified.jpg', description: [
        '<p>We tokenize real-world assets to create new investment opportunities and improve liquidity for traditionally illiquid assets.</p>',
        '<p>Our RWA tokenization covers real estate, commodities, art, and other physical assets, making them accessible through blockchain technology.</p>',
        '<p>This approach democratizes access to high-value assets and creates new revenue streams for asset owners.</p>'
      ]
    },
    { id: 4, title: 'Managing Specific Risks', helpText: 'Risk Management', img: '/risk-riskmanagement.jpg', description: [
        'Our comprehensive risk management framework addresses the unique challenges of digital asset investing:',
        renderAccordion(risks, activeIndex, toggleAccordion)
      ]
    }
  ];

  return (
    <div>
      <FullHeightHero
        title="Strategies"
        subtitle="Innovative approaches to modern portfolio management"
        backgroundImage="/loading-strategies.jpg"
      />
      
      <div className="page-content-container">
        {/* Section Tagline */}
        <section id='strategies-tagline'
        className="py-5"
        style={{ backgroundColor: STRATEGIES_COLORS.DARK_BLUE }}>
        <div className="container">
          <p className="h2 ms-4 text-white">Innovative strategies powering the future of capital allocation</p>
        </div>
      </section>

      {/* CTA Section #1 */}
      <SectionFrame shape="circles" />
      <section id='strategies-cta1' className="full-width-dark-section" style={{ backgroundColor: STRATEGIES_COLORS.DARK_BLUE, paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0' }}>
        <div className="container text-center-mbl text-white" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
          <CTASection
            title="Sign up for the Moolah Capital Newsletter"
            cta="Click here to sign up"
            link="/addemail#email-signup"
            theme='signup'
            titleStyle={{ color: 'white' }}
          />
        </div>
      </section>
      <SectionFrame2 shape="lines" />

      {/* Strategy Sections with Alternating Backgrounds */}
      <section id="strategies-list">
        {cardStrategyList.map((strategy, index) => (
          <section key={strategy.id} className="py-5 d-flex align-items-center" style={{
            backgroundColor: index % 2 === 0 ? STRATEGIES_COLORS.DARK_BLUE : STRATEGIES_COLORS.MEDIUM_BLUE,
            minHeight: '500px' // Ensure consistent section height
          }}>
            <div className="container" style={{ color: index % 2 === 0 ? 'var(--text-white)' : 'var(--text-medium-blue)' }}>
              <div
                className={`row align-items-center ${index % 2 !== 0 ? 'flex-column flex-md-row' : 'flex-md-row-reverse flex-column'}`}
              >
                <div className="col-md-6 my-3 d-flex align-items-center justify-content-center">
                  <div className="w-100 px-4">
                    <div className="text-start mb-4 d-flex align-items-center" style={{ gap: '0.5rem' }}>
                      <div className="line d-none d-md-block" style={{ backgroundColor: 'white' }}></div>
                      <p className="h2 mb-0 text-start">{strategy.helpText}</p>
                    </div>
                    {strategy.title === 'Managing Specific Risks' ? (
                      <div>
                        {strategy.description.map((desc, idx) => (
                          <div key={idx} className="mb-3">
                            {typeof desc === 'string' ? (
                              <p className="body-text text-start">{desc}</p>
                            ) : (
                              desc
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div>
                        {strategy.description.map((desc, idx) => (
                          <div key={idx} className="mb-3" dangerouslySetInnerHTML={{ __html: desc }}></div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <div className="col-md-6 my-3 d-flex align-items-center justify-content-center">
                  <div className="w-100 px-4">
                    <img
                      src={strategy.img}
                      alt={strategy.title}
                      className="img-fluid rounded"
                      style={{ maxHeight: '400px', width: '100%', objectFit: 'cover' }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </section>
        ))}
      </section>

      {/* CTA Section */}
      <SectionFrame shape="triangles" />
      <section id='strategies-cta' className="full-width-dark-section" style={{ backgroundColor: STRATEGIES_COLORS.DARK_BLUE, paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0' }}>
        <div className="container text-center-mbl text-white" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
          <CTASection
            title="Let's Build Smarter Portfolios - Together"
            cta="Click here to view Funds"
            link="/funds"
            theme='learn'
            titleStyle={{ color: 'white' }}
          />
        </div>
      </section>
      <SectionFrame2 shape="mixed" />
      </div>
    </div>
  );
};

export default Strategies;
