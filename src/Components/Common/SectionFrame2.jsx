import React from 'react';

const SectionFrame2 = ({
  color = 'var(--frame2-color)',
  width = 'var(--frame2-width)',
  className = '',
  style = {},
  shape = 'curve' // Options: 'wave', 'diagonal', 'zigzag', 'curve', 'rectangle', 'triangle'
}) => {

  const renderShape = () => {
    switch(shape) {
      case 'wave':
        return (
          <div
            className={`section-frame2 full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              overflow: 'hidden',
              ...style
            }}
          >
            <svg
              viewBox="0 0 1200 120"
              preserveAspectRatio="none"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                fill: color
              }}
            >
              <path d="M0,40 C200,80 400,20 600,60 C800,100 1000,30 1200,70 L1200,120 L0,120 Z" />
            </svg>
          </div>
        );

      case 'diagonal':
        return (
          <div
            className={`section-frame2 full-width-frame ${className}`}
            style={{
              height: width,
              background: `linear-gradient(-45deg, ${color} 0%, ${color} 50%, transparent 50%)`,
              ...style
            }}
          />
        );

      case 'zigzag':
        return (
          <div
            className={`section-frame2 full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              overflow: 'hidden',
              ...style
            }}
          >
            <svg
              viewBox="0 0 1200 120"
              preserveAspectRatio="none"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                fill: color
              }}
            >
              <path d="M0,120 L0,80 L150,40 L300,100 L450,20 L600,80 L750,40 L900,100 L1050,20 L1200,60 L1200,120 Z" />
            </svg>
          </div>
        );

      case 'curve':
        return (
          <div
            className={`section-frame2 full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              overflow: 'hidden',
              ...style
            }}
          >
            <svg
              viewBox="0 0 1200 120"
              preserveAspectRatio="none"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                fill: color
              }}
            >
              <path d="M0,120 L0,40 Q600,100 1200,40 L1200,120 Z" />
            </svg>
          </div>
        );

      case 'triangle':
        return (
          <div
            className={`section-frame2 full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              overflow: 'hidden',
              ...style
            }}
          >
            <svg
              viewBox="0 0 1200 120"
              preserveAspectRatio="none"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                fill: color
              }}
            >
              <path d="M0,120 L600,20 L1200,120 Z" />
            </svg>
          </div>
        );

      default: // rectangle
        return (
          <div
            className={`section-frame2 full-width-frame ${className}`}
            style={{
              backgroundColor: color,
              height: width,
              ...style
            }}
          />
        );
    }
  };

  return renderShape();
};

export default SectionFrame2;
