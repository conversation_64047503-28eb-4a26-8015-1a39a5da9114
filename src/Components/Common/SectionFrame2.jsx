import React from 'react';

const SectionFrame2 = ({
  color = 'var(--frame2-color)',
  width = 'var(--frame2-width)',
  className = '',
  style = {},
  shape = 'dots' // Options: 'dots', 'lines', 'mixed', 'minimal', 'rectangle'
}) => {

  const renderShape = () => {
    switch(shape) {
      case 'dots':
        return (
          <div
            className={`section-frame2 full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              backgroundColor: color,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-around',
              padding: '0 10%',
              ...style
            }}
          >
            {[...Array(9)].map((_, i) => (
              <div
                key={i}
                style={{
                  width: `${8 + (i % 4) * 4}px`,
                  height: `${8 + (i % 4) * 4}px`,
                  backgroundColor: `rgba(255, 255, 255, ${0.15 + (i % 3) * 0.05})`,
                  borderRadius: '50%'
                }}
              />
            ))}
          </div>
        );

      case 'lines':
        return (
          <div
            className={`section-frame2 full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              backgroundColor: color,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '20px',
              ...style
            }}
          >
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                style={{
                  width: '2px',
                  height: `${16 + (i % 3) * 8}px`,
                  backgroundColor: `rgba(255, 255, 255, ${0.1 + (i % 2) * 0.1})`,
                  borderRadius: '1px'
                }}
              />
            ))}
          </div>
        );

      case 'mixed':
        return (
          <div
            className={`section-frame2 full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              backgroundColor: color,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '0 8%',
              ...style
            }}
          >
            {/* Left cluster */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '16px',
                height: '16px',
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                borderRadius: '50%'
              }} />
              <div style={{
                width: '3px',
                height: '20px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '1px'
              }} />
              <div style={{
                width: '12px',
                height: '12px',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '1px'
              }} />
            </div>

            {/* Center elements */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div style={{
                width: '8px',
                height: '8px',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '50%'
              }} />
              <div style={{
                width: '10px',
                height: '10px',
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                borderRadius: '50%'
              }} />
              <div style={{
                width: '6px',
                height: '6px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '50%'
              }} />
            </div>

            {/* Right cluster */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <div style={{
                width: '14px',
                height: '14px',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '2px',
                transform: 'rotate(45deg)'
              }} />
              <div style={{
                width: '18px',
                height: '18px',
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                borderRadius: '50%'
              }} />
            </div>
          </div>
        );

      case 'minimal':
        return (
          <div
            className={`section-frame2 full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              backgroundColor: color,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              ...style
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: '40px' }}>
              <div style={{
                width: '20px',
                height: '20px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '50%'
              }} />
              <div style={{
                width: '16px',
                height: '16px',
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                borderRadius: '2px'
              }} />
              <div style={{
                width: '18px',
                height: '18px',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '50%'
              }} />
            </div>
          </div>
        );

      default: // rectangle
        return (
          <div
            className={`section-frame2 full-width-frame ${className}`}
            style={{
              backgroundColor: color,
              height: width,
              ...style
            }}
          />
        );
    }
  };

  return renderShape();
};

export default SectionFrame2;
