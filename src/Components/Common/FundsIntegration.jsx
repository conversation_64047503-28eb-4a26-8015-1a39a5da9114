import React from 'react';
import AnimatedBackground from './AnimatedBackground';

// Integration example for Funds page sections

const FundsIntegrationExample = () => {
  return (
    <div>
      {/* 1. FUNDS INTRO SECTION (Dark Blue) */}
      <section id="funds-intro" className='position-relative text-white overflow-hidden full-width-dark-section' style={{ 
        backgroundColor: '#040a0f', // FUNDS_COLORS.DARK_BLUE
        minHeight: '500px', 
        paddingTop: '60px', 
        paddingBottom: '60px', 
        paddingLeft: '0', 
        paddingRight: '0', 
        display: 'flex', 
        alignItems: 'center',
        position: 'relative',  // ENSURE THIS IS SET
        overflow: 'hidden'     // ENSURE THIS IS SET
      }}>
        {/* ADD: Corner-focused animation for intro impact */}
        <AnimatedBackground
          density="medium"
          colors={['rgba(0, 255, 200, 0.15)', 'rgba(99, 91, 255, 0.15)']}
          position="corner"
          speed="slow"
          shapes={['circle', 'square', 'diamond']}
        />

        {/* Your existing content - ENSURE zIndex */}
        <div className="container-fluid text-white" style={{ 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing funds intro content */}
        </div>
      </section>

      {/* 2. SMART BETA FUNDS SECTION (Medium Blue) */}
      <section id="smartbeta" className='position-relative overflow-hidden full-width-medium-section' style={{ 
        backgroundColor: '#b8e6ff', // FUNDS_COLORS.MEDIUM_BLUE
        minHeight: '500px', 
        paddingTop: '60px', 
        paddingBottom: '60px', 
        paddingLeft: '0', 
        paddingRight: '0', 
        display: 'flex', 
        alignItems: 'center', 
        color: 'var(--text-medium-blue)',
        position: 'relative',  // ENSURE THIS IS SET
        overflow: 'hidden'     // ENSURE THIS IS SET
      }}>
        {/* ADD: Center animation with dark shapes */}
        <AnimatedBackground
          density="medium"
          colors={['rgba(4, 10, 15, 0.12)', 'rgba(4, 10, 15, 0.08)']}
          position="center"
          speed="medium"
          shapes={['triangle', 'circle', 'square']}
        />

        {/* Your existing content - ENSURE zIndex */}
        <div className="container-fluid" style={{ 
          color: 'var(--text-medium-blue)', 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing smart beta content */}
        </div>
      </section>

      {/* 3. GENAI FUNDS SECTION (Dark Blue) */}
      <section id="genai" className='position-relative text-white overflow-hidden full-width-dark-section' style={{ 
        backgroundColor: '#040a0f', // FUNDS_COLORS.DARK_BLUE
        minHeight: '500px', 
        paddingTop: '60px', 
        paddingBottom: '60px', 
        paddingLeft: '0', 
        paddingRight: '0', 
        display: 'flex', 
        alignItems: 'center',
        position: 'relative',  // ENSURE THIS IS SET
        overflow: 'hidden'     // ENSURE THIS IS SET
      }}>
        {/* ADD: Full background with tech-focused shapes */}
        <AnimatedBackground
          density="high"
          colors={['rgba(0, 255, 200, 0.1)', 'rgba(99, 91, 255, 0.12)']}
          position="full"
          speed="medium"
          shapes={['diamond', 'square', 'circle']}
        />

        {/* Your existing content - ENSURE zIndex */}
        <div className="container-fluid text-white" style={{ 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing GenAI content */}
        </div>
      </section>

      {/* 4. FINAL CTA SECTION (Dark Blue) */}
      <section id="funds-cta" className="full-width-dark-section" style={{ 
        backgroundColor: '#040a0f', // FUNDS_COLORS.DARK_BLUE
        paddingTop: '60px', 
        paddingBottom: '60px', 
        paddingLeft: '0', 
        paddingRight: '0',
        position: 'relative',  // ADD THIS
        overflow: 'hidden'     // ADD THIS
      }}>
        {/* ADD: Subtle CTA animation */}
        <AnimatedBackground
          density="low"
          colors={['rgba(0, 255, 200, 0.08)', 'rgba(99, 91, 255, 0.08)']}
          position="full"
          speed="slow"
          shapes={['circle', 'square']}
          className="animated-background--subtle"
        />

        {/* Your existing content - ENSURE zIndex */}
        <div className="container text-white" style={{ 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing CTA content */}
        </div>
      </section>
    </div>
  );
};

export default FundsIntegrationExample;

/*
INTEGRATION STEPS FOR YOUR FUNDS PAGE:

1. Add import at the top of your Funds.jsx:
   import AnimatedBackground from '../Common/AnimatedBackground';

2. For each section, apply these patterns:

   DARK BLUE SECTIONS:
   - Use light colored shapes: rgba(0, 255, 200, 0.1-0.15) and rgba(99, 91, 255, 0.1-0.15)
   - Intro sections: position="corner" for impact
   - Content sections: position="full" for coverage
   - CTA sections: density="low" for subtlety

   MEDIUM BLUE SECTIONS:
   - Use dark colored shapes: rgba(4, 10, 15, 0.08-0.12)
   - Use position="center" for focused effect
   - Medium density for balanced visual interest

3. REQUIRED STYLING:
   - Ensure position: 'relative' on all sections
   - Ensure overflow: 'hidden' on all sections
   - Add position: 'relative', zIndex: 2 to content containers

4. ANIMATION SPEEDS:
   - Intro sections: "slow" for professional feel
   - Content sections: "medium" for engagement
   - CTA sections: "slow" for focus
*/
