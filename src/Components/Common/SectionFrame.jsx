import React from 'react';

const SectionFrame = ({
  color = 'var(--frame-color)',
  width = 'var(--frame-width)',
  className = '',
  style = {},
  shape = 'wave' // Options: 'wave', 'diagonal', 'zigzag', 'curve', 'rectangle'
}) => {

  const renderShape = () => {
    switch(shape) {
      case 'wave':
        return (
          <div
            className={`section-frame full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              overflow: 'hidden',
              ...style
            }}
          >
            <svg
              viewBox="0 0 1200 120"
              preserveAspectRatio="none"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                fill: color
              }}
            >
              <path d="M0,60 C300,20 600,100 900,40 C1050,10 1150,80 1200,60 L1200,120 L0,120 Z" />
            </svg>
          </div>
        );

      case 'diagonal':
        return (
          <div
            className={`section-frame full-width-frame ${className}`}
            style={{
              height: width,
              background: `linear-gradient(45deg, ${color} 0%, ${color} 50%, transparent 50%)`,
              ...style
            }}
          />
        );

      case 'zigzag':
        return (
          <div
            className={`section-frame full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              overflow: 'hidden',
              ...style
            }}
          >
            <svg
              viewBox="0 0 1200 120"
              preserveAspectRatio="none"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                fill: color
              }}
            >
              <path d="M0,120 L0,60 L200,20 L400,80 L600,40 L800,100 L1000,60 L1200,20 L1200,120 Z" />
            </svg>
          </div>
        );

      case 'curve':
        return (
          <div
            className={`section-frame full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              overflow: 'hidden',
              ...style
            }}
          >
            <svg
              viewBox="0 0 1200 120"
              preserveAspectRatio="none"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                fill: color
              }}
            >
              <path d="M0,120 L0,80 Q600,20 1200,80 L1200,120 Z" />
            </svg>
          </div>
        );

      default: // rectangle
        return (
          <div
            className={`section-frame full-width-frame ${className}`}
            style={{
              backgroundColor: color,
              height: width,
              ...style
            }}
          />
        );
    }
  };

  return renderShape();
};

export default SectionFrame;
