import React from 'react';

const SectionFrame = ({
  color = 'var(--frame-color)',
  width = 'var(--frame-width)',
  className = '',
  style = {},
  shape = 'geometric' // Options: 'geometric', 'circles', 'squares', 'triangles', 'rectangle'
}) => {

  const renderShape = () => {
    switch(shape) {
      case 'geometric':
        return (
          <div
            className={`section-frame full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              backgroundColor: color,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '0 5%',
              ...style
            }}
          >
            {/* Left side geometric elements */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
              <div style={{
                width: '24px',
                height: '24px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '50%'
              }} />
              <div style={{
                width: '16px',
                height: '16px',
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                transform: 'rotate(45deg)'
              }} />
              <div style={{
                width: '20px',
                height: '20px',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '2px'
              }} />
            </div>

            {/* Right side geometric elements */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
              <div style={{
                width: '18px',
                height: '18px',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '50%'
              }} />
              <div style={{
                width: '22px',
                height: '22px',
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                borderRadius: '3px'
              }} />
              <div style={{
                width: '14px',
                height: '14px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                transform: 'rotate(45deg)'
              }} />
            </div>
          </div>
        );

      case 'circles':
        return (
          <div
            className={`section-frame full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              backgroundColor: color,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '30px',
              ...style
            }}
          >
            {[...Array(7)].map((_, i) => (
              <div
                key={i}
                style={{
                  width: `${12 + (i % 3) * 8}px`,
                  height: `${12 + (i % 3) * 8}px`,
                  backgroundColor: `rgba(255, 255, 255, ${0.1 + (i % 3) * 0.05})`,
                  borderRadius: '50%'
                }}
              />
            ))}
          </div>
        );

      case 'squares':
        return (
          <div
            className={`section-frame full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              backgroundColor: color,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '25px',
              ...style
            }}
          >
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                style={{
                  width: `${14 + (i % 2) * 6}px`,
                  height: `${14 + (i % 2) * 6}px`,
                  backgroundColor: `rgba(255, 255, 255, ${0.1 + (i % 2) * 0.1})`,
                  borderRadius: '2px',
                  transform: i % 3 === 1 ? 'rotate(45deg)' : 'none'
                }}
              />
            ))}
          </div>
        );

      case 'triangles':
        return (
          <div
            className={`section-frame full-width-frame ${className}`}
            style={{
              height: width,
              position: 'relative',
              backgroundColor: color,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '35px',
              ...style
            }}
          >
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                style={{
                  width: 0,
                  height: 0,
                  borderLeft: `${8 + i * 2}px solid transparent`,
                  borderRight: `${8 + i * 2}px solid transparent`,
                  borderBottom: `${14 + i * 3}px solid rgba(255, 255, 255, ${0.1 + (i % 2) * 0.1})`,
                  transform: i % 2 === 1 ? 'rotate(180deg)' : 'none'
                }}
              />
            ))}
          </div>
        );

      default: // rectangle
        return (
          <div
            className={`section-frame full-width-frame ${className}`}
            style={{
              backgroundColor: color,
              height: width,
              ...style
            }}
          />
        );
    }
  };

  return renderShape();
};

export default SectionFrame;
