import React from 'react';
import AnimatedBackground from './AnimatedBackground';

// This shows exactly how to integrate AnimatedBackground into your Homepage sections

const HomepageIntegrationExample = () => {
  return (
    <div>
      {/* 1. HERO VIDEO SECTION - Add Stripe-style corner animation */}
      <section className="video-container" style={{ 
        position: 'relative', 
        width: '100%', 
        height: '100vh', 
        zIndex: 0,
        backgroundColor: 'transparent',
        marginTop: '0',
        paddingTop: '0',
        overflow: 'hidden'  // ADD THIS
      }}>
        {/* ADD: Stripe-style corner animation */}
        <AnimatedBackground
          density="medium"
          colors={['rgba(0, 255, 200, 0.3)', 'rgba(99, 91, 255, 0.3)']}
          position="corner"
          speed="slow"
          shapes={['circle', 'square', 'diamond']}
          className="animated-background--corner"
        />

        {/* Your existing video element */}
        <video
          id="homepage-video"
          autoPlay
          className="background-video"
          loop
          muted
          playsInline
          preload="auto"
          src="/moolah-home.mp4"
          style={{ 
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            zIndex: 0
          }}
        />

        {/* Content overlay - ADD zIndex: 3 */}
        <div className="video-overlay-content" style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 3,  // CHANGE FROM 2 TO 3
          textAlign: 'center',
          color: 'white'
        }}>
          <h1 className="display-4">Simple and Successful</h1>
          <h1 className="display-4">Crypto Investing</h1>
        </div>
      </section>

      {/* 2. DARK BLUE CTA SECTIONS - Add subtle full background */}
      <section id="funds-cta1" className="full-width-dark-section" style={{ 
        backgroundColor: '#040a0f', // Your DARK_BLUE
        paddingTop: '60px', 
        paddingBottom: '60px', 
        paddingLeft: '0', 
        paddingRight: '0',
        position: 'relative',  // ADD THIS
        overflow: 'hidden'     // ADD THIS
      }}>
        {/* ADD: Subtle full background animation */}
        <AnimatedBackground
          density="low"
          colors={['rgba(0, 255, 200, 0.1)', 'rgba(99, 91, 255, 0.1)']}
          position="full"
          speed="slow"
          shapes={['circle', 'square']}
          className="animated-background--subtle"
        />

        {/* Your existing content - ADD zIndex */}
        <div className="container text-center-mbl text-white" style={{ 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing CTASection component */}
        </div>
      </section>

      {/* 3. WHY CHOOSE SECTION - Add background animation */}
      <section id='home-why' className="full-width-dark-section" style={{ 
        backgroundColor: '#040a0f', 
        paddingTop: '120px', 
        paddingBottom: '120px', 
        paddingLeft: '0', 
        paddingRight: '0',
        position: 'relative',  // ADD THIS
        overflow: 'hidden'     // ADD THIS
      }}>
        {/* ADD: Medium density animation */}
        <AnimatedBackground
          density="medium"
          colors={['rgba(0, 255, 200, 0.08)', 'rgba(99, 91, 255, 0.08)']}
          position="full"
          speed="slow"
          shapes={['circle', 'triangle', 'square']}
        />

        {/* Your existing content - ADD zIndex */}
        <div className="container text-white" style={{ 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          <h2 className="h2 text-start mb-5">Why Choose Moolah Capital?</h2>
          {/* Your existing reasons cards */}
        </div>
      </section>

      {/* 4. MEDIUM BLUE SECTIONS - Add contrasting animation */}
      <section style={{ 
        backgroundColor: '#b8e6ff', // Your MEDIUM_BLUE
        minHeight: '500px',
        paddingTop: '60px',
        paddingBottom: '60px',
        position: 'relative',  // ADD THIS
        overflow: 'hidden'     // ADD THIS
      }}>
        {/* ADD: Dark shapes on light background */}
        <AnimatedBackground
          density="medium"
          colors={['rgba(4, 10, 15, 0.1)', 'rgba(4, 10, 15, 0.15)']}
          position="center"
          speed="medium"
          shapes={['circle', 'square', 'triangle']}
        />

        {/* Your existing content - ADD zIndex */}
        <div className="container" style={{ 
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing section content */}
        </div>
      </section>
    </div>
  );
};

export default HomepageIntegrationExample;

/*
INTEGRATION STEPS FOR YOUR HOMEPAGE:

1. Add import at the top of your Homepage.jsx:
   import AnimatedBackground from '../Common/AnimatedBackground';

2. For each section, make these changes:

   REQUIRED STYLING CHANGES:
   - Add: position: 'relative'
   - Add: overflow: 'hidden'
   - For content divs, add: position: 'relative', zIndex: 2

   HERO SECTION:
   - Add AnimatedBackground with position="corner" (Stripe-style)
   - Increase content zIndex from 2 to 3

   DARK BLUE SECTIONS:
   - Add AnimatedBackground with light colored shapes
   - Use low density for CTA sections, medium for content sections

   MEDIUM BLUE SECTIONS:
   - Add AnimatedBackground with dark colored shapes
   - Use center positioning for focused effect

3. Test each section individually to ensure animations work properly
*/
