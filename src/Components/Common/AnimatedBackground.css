/* Animated Background Styles */
.animated-background {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

.animated-background > div {
  will-change: transform, opacity;
  backface-visibility: hidden;
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
  .animated-background {
    display: none;
  }
}

/* Mobile optimizations - reduce complexity on smaller screens */
@media (max-width: 768px) {
  .animated-background {
    opacity: 0.7;
  }
  
  .animated-background > div:nth-child(n+8) {
    display: none;
  }
}

@media (max-width: 480px) {
  .animated-background {
    opacity: 0.5;
  }
  
  .animated-background > div:nth-child(n+5) {
    display: none;
  }
}

/* Specific positioning variants */
.animated-background--corner {
  width: 50%;
  height: 70%;
}

.animated-background--center {
  width: 60%;
  height: 60%;
  left: 20%;
  top: 20%;
}

.animated-background--full {
  width: 100%;
  height: 100%;
}

/* Theme variants */
.animated-background--subtle {
  opacity: 0.6;
}

.animated-background--prominent {
  opacity: 0.9;
}

/* Blur effect for background elements */
.animated-background--blurred > div {
  filter: blur(0.5px);
}

/* Gradient overlay option */
.animated-background--gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 50%);
  pointer-events: none;
  z-index: 1;
}
