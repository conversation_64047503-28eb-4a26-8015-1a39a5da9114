import React, { useMemo } from 'react';
import './AnimatedBackground.css';

const AnimatedBackground = ({ 
  density = 'medium', // 'low', 'medium', 'high'
  colors = ['#00ffc8', '#635bff'], // Default to your frame colors
  position = 'corner', // 'corner', 'full', 'center'
  speed = 'slow', // 'slow', 'medium', 'fast'
  shapes = ['circle', 'square', 'triangle'], // Available shapes
  className = ''
}) => {
  
  // Generate random shapes based on density
  const generateShapes = useMemo(() => {
    const densityMap = {
      low: 8,
      medium: 12,
      high: 18
    };
    
    const count = densityMap[density] || 12;
    const shapes_array = [];
    
    for (let i = 0; i < count; i++) {
      const shape = shapes[Math.floor(Math.random() * shapes.length)];
      const color = colors[Math.floor(Math.random() * colors.length)];
      const size = 20 + Math.random() * 40; // 20-60px
      const opacity = 0.1 + Math.random() * 0.15; // 0.1-0.25
      const duration = speed === 'slow' ? 8 + Math.random() * 4 : 
                      speed === 'medium' ? 5 + Math.random() * 3 : 
                      3 + Math.random() * 2; // Animation duration
      const delay = Math.random() * 5; // Stagger animations
      
      // Position based on layout preference
      let left, top;
      if (position === 'corner') {
        left = Math.random() * 40; // Left 40% of screen
        top = Math.random() * 60; // Top 60% of screen
      } else if (position === 'center') {
        left = 30 + Math.random() * 40; // Center area
        top = 20 + Math.random() * 60;
      } else { // full
        left = Math.random() * 100;
        top = Math.random() * 100;
      }
      
      shapes_array.push({
        id: i,
        shape,
        color,
        size,
        opacity,
        duration,
        delay,
        left,
        top,
        rotation: Math.random() * 360
      });
    }
    
    return shapes_array;
  }, [density, colors, position, speed, shapes]);
  
  const renderShape = (shapeData) => {
    const { shape, color, size, opacity, duration, delay, left, top, rotation, id } = shapeData;
    
    const baseStyle = {
      position: 'absolute',
      left: `${left}%`,
      top: `${top}%`,
      width: `${size}px`,
      height: `${size}px`,
      backgroundColor: color,
      opacity: opacity,
      animation: `float-${id} ${duration}s ease-in-out infinite`,
      animationDelay: `${delay}s`,
      transform: `rotate(${rotation}deg)`,
      pointerEvents: 'none'
    };
    
    switch (shape) {
      case 'circle':
        return (
          <div
            key={id}
            style={{
              ...baseStyle,
              borderRadius: '50%'
            }}
          />
        );
      
      case 'square':
        return (
          <div
            key={id}
            style={{
              ...baseStyle,
              borderRadius: '3px'
            }}
          />
        );
      
      case 'triangle':
        return (
          <div
            key={id}
            style={{
              ...baseStyle,
              width: 0,
              height: 0,
              backgroundColor: 'transparent',
              borderLeft: `${size/2}px solid transparent`,
              borderRight: `${size/2}px solid transparent`,
              borderBottom: `${size}px solid ${color}`
            }}
          />
        );
      
      case 'diamond':
        return (
          <div
            key={id}
            style={{
              ...baseStyle,
              borderRadius: '3px',
              transform: `rotate(${rotation + 45}deg)`
            }}
          />
        );
      
      default:
        return null;
    }
  };
  
  // Generate unique keyframes for each shape
  const generateKeyframes = () => {
    return generateShapes.map(shape => {
      const moveX = (Math.random() - 0.5) * 60; // -30 to 30px movement
      const moveY = (Math.random() - 0.5) * 80; // -40 to 40px movement
      const rotateAmount = (Math.random() - 0.5) * 180; // Rotation variation
      const scaleAmount = 0.8 + Math.random() * 0.4; // 0.8 to 1.2 scale
      
      return `
        @keyframes float-${shape.id} {
          0%, 100% { 
            transform: translate(0px, 0px) rotate(${shape.rotation}deg) scale(1);
          }
          25% { 
            transform: translate(${moveX * 0.3}px, ${moveY * 0.5}px) rotate(${shape.rotation + rotateAmount * 0.3}deg) scale(${scaleAmount});
          }
          50% { 
            transform: translate(${moveX}px, ${moveY}px) rotate(${shape.rotation + rotateAmount}deg) scale(1);
          }
          75% { 
            transform: translate(${moveX * 0.7}px, ${moveY * 0.3}px) rotate(${shape.rotation + rotateAmount * 0.7}deg) scale(${scaleAmount});
          }
        }
      `;
    }).join('\n');
  };
  
  return (
    <>
      <style>
        {generateKeyframes()}
      </style>
      <div 
        className={`animated-background ${className}`}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          overflow: 'hidden',
          pointerEvents: 'none',
          zIndex: -1
        }}
      >
        {generateShapes.map(renderShape)}
      </div>
    </>
  );
};

export default AnimatedBackground;
