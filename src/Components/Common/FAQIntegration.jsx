import React from 'react';
import AnimatedBackground from './AnimatedBackground';

// Integration example for FAQ page sections

const FAQIntegrationExample = () => {
  return (
    <div>
      {/* 1. INVESTING IN FUNDS SECTION (Dark Blue) */}
      <section className="py-5 full-width-dark-section" style={{ 
        backgroundColor: '#040a0f', // FAQ_COLORS.DARK_BLUE
        minHeight: '500px', 
        paddingTop: '60px', 
        paddingBottom: '60px', 
        paddingLeft: '0', 
        paddingRight: '0', 
        display: 'flex', 
        alignItems: 'center',
        position: 'relative',  // ADD THIS
        overflow: 'hidden'     // ADD THIS
      }}>
        {/* ADD: Corner animation for section start */}
        <AnimatedBackground
          density="medium"
          colors={['rgba(0, 255, 200, 0.12)', 'rgba(99, 91, 255, 0.12)']}
          position="corner"
          speed="slow"
          shapes={['circle', 'square']}
        />

        {/* Your existing content - ADD zIndex */}
        <div className="container text-white" style={{ 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing FAQ content */}
        </div>
      </section>

      {/* 2. UNDERSTANDING INVESTMENTS SECTION (Medium Blue) */}
      <section className="py-5 full-width-medium-section" style={{ 
        backgroundColor: '#b8e6ff', // FAQ_COLORS.MEDIUM_BLUE
        minHeight: '500px', 
        paddingTop: '60px', 
        paddingBottom: '60px', 
        paddingLeft: '0', 
        paddingRight: '0', 
        display: 'flex', 
        alignItems: 'center', 
        color: 'var(--text-medium-blue)',
        position: 'relative',  // ADD THIS
        overflow: 'hidden'     // ADD THIS
      }}>
        {/* ADD: Center animation with educational feel */}
        <AnimatedBackground
          density="medium"
          colors={['rgba(4, 10, 15, 0.1)', 'rgba(4, 10, 15, 0.08)']}
          position="center"
          speed="medium"
          shapes={['triangle', 'circle']}
        />

        {/* Your existing content - ADD zIndex */}
        <div className="container" style={{ 
          color: 'var(--text-medium-blue)', 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing understanding investments content */}
        </div>
      </section>

      {/* 3. UNDERSTANDING MOOLAH FUNDS SECTION (Dark Blue) */}
      <section className="py-5 full-width-dark-section" style={{ 
        backgroundColor: '#040a0f', // FAQ_COLORS.DARK_BLUE
        minHeight: '500px', 
        paddingTop: '60px', 
        paddingBottom: '60px', 
        paddingLeft: '0', 
        paddingRight: '0', 
        display: 'flex', 
        alignItems: 'center',
        position: 'relative',  // ADD THIS
        overflow: 'hidden'     // ADD THIS
      }}>
        {/* ADD: Full background for comprehensive section */}
        <AnimatedBackground
          density="medium"
          colors={['rgba(0, 255, 200, 0.1)', 'rgba(99, 91, 255, 0.1)']}
          position="full"
          speed="slow"
          shapes={['square', 'diamond', 'circle']}
        />

        {/* Your existing content - ADD zIndex */}
        <div className="container text-white" style={{ 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing Moolah funds content */}
        </div>
      </section>

      {/* 4. INVESTMENT GOALS SECTION (Medium Blue) */}
      <section className="py-5 full-width-medium-section" style={{ 
        backgroundColor: '#b8e6ff', // FAQ_COLORS.MEDIUM_BLUE
        minHeight: '500px', 
        paddingTop: '60px', 
        paddingBottom: '60px', 
        paddingLeft: '0', 
        paddingRight: '0', 
        display: 'flex', 
        alignItems: 'center', 
        color: 'var(--text-medium-blue)',
        position: 'relative',  // ADD THIS
        overflow: 'hidden'     // ADD THIS
      }}>
        {/* ADD: Subtle center animation */}
        <AnimatedBackground
          density="low"
          colors={['rgba(4, 10, 15, 0.08)', 'rgba(4, 10, 15, 0.06)']}
          position="center"
          speed="slow"
          shapes={['circle', 'triangle']}
        />

        {/* Your existing content - ADD zIndex */}
        <div className="container" style={{ 
          color: 'var(--text-medium-blue)', 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing investment goals content */}
        </div>
      </section>

      {/* 5. INVESTMENT COSTS SECTION (Dark Blue) */}
      <section className="py-5 full-width-dark-section" style={{ 
        backgroundColor: '#040a0f', // FAQ_COLORS.DARK_BLUE
        minHeight: '500px', 
        paddingTop: '60px', 
        paddingBottom: '60px', 
        paddingLeft: '0', 
        paddingRight: '0', 
        display: 'flex', 
        alignItems: 'center',
        position: 'relative',  // ADD THIS
        overflow: 'hidden'     // ADD THIS
      }}>
        {/* ADD: Corner animation for costs section */}
        <AnimatedBackground
          density="medium"
          colors={['rgba(0, 255, 200, 0.1)', 'rgba(99, 91, 255, 0.12)']}
          position="corner"
          speed="slow"
          shapes={['square', 'circle']}
        />

        {/* Your existing content - ADD zIndex */}
        <div className="container text-white" style={{ 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing investment costs content */}
        </div>
      </section>

      {/* 6. CTA SECTION (Medium Blue) */}
      <section id="faq-cta" className="full-width-medium-section" style={{ 
        backgroundColor: '#b8e6ff', // FAQ_COLORS.MEDIUM_BLUE
        paddingTop: '60px', 
        paddingBottom: '60px', 
        paddingLeft: '0', 
        paddingRight: '0', 
        color: 'var(--text-medium-blue)',
        position: 'relative',  // ADD THIS
        overflow: 'hidden'     // ADD THIS
      }}>
        {/* ADD: Subtle CTA animation */}
        <AnimatedBackground
          density="low"
          colors={['rgba(4, 10, 15, 0.06)', 'rgba(4, 10, 15, 0.08)']}
          position="full"
          speed="slow"
          shapes={['circle', 'square']}
          className="animated-background--subtle"
        />

        {/* Your existing content - ADD zIndex */}
        <div className="container text-center-mbl" style={{ 
          color: 'var(--text-medium-blue)', 
          paddingLeft: '20px', 
          paddingRight: '20px',
          position: 'relative',  // ADD THIS
          zIndex: 2              // ADD THIS
        }}>
          {/* Your existing CTA content */}
        </div>
      </section>
    </div>
  );
};

export default FAQIntegrationExample;

/*
INTEGRATION STEPS FOR YOUR FAQ PAGE:

1. Add import at the top of your FAQ.jsx:
   import AnimatedBackground from '../Common/AnimatedBackground';

2. FAQ-specific animation patterns:

   ALTERNATING SECTIONS:
   - Dark blue sections: Light colored shapes
   - Medium blue sections: Dark colored shapes
   - Vary position: corner → center → full → center → corner → full

   EDUCATIONAL FEEL:
   - Use slower speeds for better readability
   - Lower density for less distraction
   - Shapes that suggest learning: circles, triangles

3. SECTION-SPECIFIC CONFIGURATIONS:

   Opening sections: position="corner" for impact
   Content sections: position="center" or "full" 
   CTA sections: density="low" for focus

4. REQUIRED STYLING CHANGES:
   - Add position: 'relative' to all sections
   - Add overflow: 'hidden' to all sections  
   - Add position: 'relative', zIndex: 2 to content containers

5. ACCESSIBILITY:
   - Animations automatically respect prefers-reduced-motion
   - Low opacity ensures text remains readable
   - Subtle movement doesn't interfere with reading
*/
