import React from 'react';
import AnimatedBackground from './AnimatedBackground';

// This is a demo component showing how to integrate AnimatedBackground
// into your existing sections

const AnimatedBackgroundDemo = () => {
  return (
    <div>
      {/* Example 1: Hero Section with Stripe-style corner animation */}
      <section 
        style={{ 
          position: 'relative', 
          height: '100vh', 
          backgroundColor: '#040a0f', // Your dark blue
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden'
        }}
      >
        {/* Animated Background - positioned in top-left corner like Stripe */}
        <AnimatedBackground
          density="medium"
          colors={['#00ffc8', '#635bff']} // Your frame colors
          position="corner"
          speed="slow"
          shapes={['circle', 'square', 'diamond']}
          className="animated-background--corner"
        />
        
        {/* Your content goes here */}
        <div style={{ position: 'relative', zIndex: 2, textAlign: 'center', color: 'white' }}>
          <h1>Simple and Successful</h1>
          <h1>Crypto Investing</h1>
        </div>
      </section>

      {/* Example 2: CTA Section with full background animation */}
      <section 
        style={{ 
          position: 'relative', 
          padding: '60px 20px',
          backgroundColor: '#040a0f', // Your dark blue
          overflow: 'hidden'
        }}
      >
        {/* Full background animation */}
        <AnimatedBackground
          density="low"
          colors={['rgba(0, 255, 200, 0.1)', 'rgba(99, 91, 255, 0.1)']}
          position="full"
          speed="slow"
          shapes={['circle', 'square']}
          className="animated-background--subtle"
        />
        
        {/* Your CTA content */}
        <div className="container text-center" style={{ position: 'relative', zIndex: 2, color: 'white' }}>
          <h2>Discover the Right Funds for Your Goals</h2>
          <button className="btn btn-primary">Click here to view Funds</button>
        </div>
      </section>

      {/* Example 3: Medium blue section with center animation */}
      <section 
        style={{ 
          position: 'relative', 
          padding: '60px 20px',
          backgroundColor: '#b8e6ff', // Your medium blue
          overflow: 'hidden'
        }}
      >
        {/* Center-focused animation */}
        <AnimatedBackground
          density="medium"
          colors={['rgba(4, 10, 15, 0.1)', 'rgba(4, 10, 15, 0.15)']} // Dark shapes on light background
          position="center"
          speed="medium"
          shapes={['triangle', 'circle', 'square']}
          className="animated-background--center"
        />
        
        {/* Your content */}
        <div className="container" style={{ position: 'relative', zIndex: 2, color: '#040a0f' }}>
          <h2>About Moolah Capital</h2>
          <p>We build a select range of globally diversified crypto funds...</p>
        </div>
      </section>
    </div>
  );
};

export default AnimatedBackgroundDemo;

/*
INTEGRATION INSTRUCTIONS:

1. Import the AnimatedBackground component:
   import AnimatedBackground from '../Common/AnimatedBackground';

2. Add it to any section that needs animated background:
   <section style={{ position: 'relative', overflow: 'hidden' }}>
     <AnimatedBackground
       density="medium"           // 'low', 'medium', 'high'
       colors={['#00ffc8', '#635bff']}  // Your brand colors
       position="corner"          // 'corner', 'center', 'full'
       speed="slow"              // 'slow', 'medium', 'fast'
       shapes={['circle', 'square']}    // Available shapes
     />
     <div style={{ position: 'relative', zIndex: 2 }}>
       Your content here
     </div>
   </section>

3. Key styling requirements:
   - Parent section needs: position: 'relative', overflow: 'hidden'
   - Content needs: position: 'relative', zIndex: 2 (to appear above animation)
   - AnimatedBackground automatically gets zIndex: -1

4. Recommended configurations:

   For Hero sections (Stripe-style):
   - density: "medium"
   - position: "corner" 
   - speed: "slow"
   - colors: Your brand colors with low opacity

   For CTA sections:
   - density: "low"
   - position: "full"
   - speed: "slow"
   - colors: Semi-transparent versions of your colors

   For content sections:
   - density: "medium"
   - position: "center"
   - speed: "medium"
   - colors: Contrasting colors with low opacity

5. Performance considerations:
   - Component automatically reduces complexity on mobile
   - Respects prefers-reduced-motion for accessibility
   - Uses GPU-accelerated transforms for smooth animation
*/
