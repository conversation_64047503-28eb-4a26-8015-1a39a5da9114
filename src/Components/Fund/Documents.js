import React from 'react';
import { FaFileAlt } from 'react-icons/fa';
import './fund.css';
import { COLORS, SEMANTIC_COLORS } from '../../constants/colors';

const Documents = ({ fundName }) => {
  // Direct reference to global colors - no copies!
  const DOCUMENTS_COLORS = COLORS;

  const documents = [
    { name: "Fact sheet" },
    { name: "Product disclosure statement" },
    { name: "Annual report" },
    { name: "Half-year financial report" },
    { name: "PDS reference guide" },
    { name: "Performance summary" }
  ];

  return (
    <>
      {/* CSS Override for Documents page text visibility */}
      <style>
        {`
          /* Force black text for documents title on light background */
          .documents-container h3 {
            color: var(--text-black) !important;
            -webkit-text-fill-color: var(--text-black) !important;
            text-fill-color: var(--text-black) !important;
          }

          /* Keep white text for document cards on dark backgrounds */
          .documents-container .document-card {
            color: var(--text-white) !important;
          }

          .documents-container .document-card * {
            color: var(--text-white) !important;
          }
        `}
      </style>

      <div className="documents-container" style={{ padding: '30px' }}>
        <div className="mb-4" style={{ textAlign: 'center' }}>
          <h3 style={{
            marginBottom: 0,
            color: 'var(--text-black)',
            fontWeight: 'bold',
            textAlign: 'center'
          }}>
            <span role="img" aria-label="documents" style={{ marginRight: '8px' }}>📄</span>
            {fundName} Documents
          </h3>
        </div>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '20px',
        margin: '30px 0'
      }}>
        {documents.map((doc, i) => (
          <div key={i} className="document-card" style={{
            backgroundColor: 'var(--dark-blue)',
            border: `1px solid rgba(255, 255, 255, 0.1)`,
            borderRadius: '12px',
            padding: '25px',
            textAlign: 'center'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <FaFileAlt style={{
                fontSize: '2.5rem',
                color: 'var(--text-white)',
                marginBottom: '15px'
              }} aria-label="Document icon" />
              <h4 style={{
                color: 'var(--text-white)',
                fontWeight: 'bold',
                margin: 0,
                fontSize: '1.1rem'
              }}>{doc.name}</h4>
            </div>
            <button style={{
              backgroundColor: 'var(--button-red)',
              color: 'var(--button-text)',
              border: 'none',
              borderRadius: '8px',
              padding: '12px 20px',
              fontSize: '1rem',
              fontWeight: '500',
              cursor: 'pointer',
              width: '100%'
            }}>
              Download <span style={{ fontSize: '1rem' }}>⬇️</span>
            </button>
          </div>
        ))}
      </div>
    </div>
    </>
  );
};
export default Documents;