import React, { useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import './fund.css';
import { COLORS, SEMANTIC_COLORS } from '../../constants/colors';
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  BarElement,
  Filler,
  CategoryScale, // for X-axis
  LinearScale,   // for Y-axis
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  LineElement,
  PointElement,
  BarElement,
  Filler,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend
);



const Performance = ({ fundName, performanceData, annualPerformanceData, lineChartOptions }) => {
  // Direct reference to global colors - no copies!
  const PERFORMANCE_COLORS = COLORS;
  useEffect(() => {
    return () => {
      console.log('Performance component unmounting');
    };
  }, [performanceData, annualPerformanceData, lineChartOptions]);

  // Enhanced chart options with white axes text
  const enhancedChartOptions = {
    ...lineChartOptions,
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      ...lineChartOptions?.plugins,
      legend: {
        ...lineChartOptions?.plugins?.legend,
        labels: {
          ...lineChartOptions?.plugins?.legend?.labels,
          color: PERFORMANCE_COLORS.WHITE
        }
      }
    },
    scales: {
      x: {
        ...lineChartOptions?.scales?.x,
        ticks: {
          ...lineChartOptions?.scales?.x?.ticks,
          color: PERFORMANCE_COLORS.WHITE
        },
        grid: {
          ...lineChartOptions?.scales?.x?.grid,
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      y: {
        ...lineChartOptions?.scales?.y,
        ticks: {
          ...lineChartOptions?.scales?.y?.ticks,
          color: PERFORMANCE_COLORS.WHITE
        },
        grid: {
          ...lineChartOptions?.scales?.y?.grid,
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    }
  };
  return (
    <>
      {/* CSS Override for Performance page text visibility */}
      <style>
        {`
          /* Default white text for performance container on dark background */
          .performance-container,
          .performance-container h1,
          .performance-container h2,
          .performance-container h3,
          .performance-container h4,
          .performance-container h5,
          .performance-container h6,
          .performance-container p,
          .performance-container span,
          .performance-container div {
            color: var(--text-white) !important;
            -webkit-text-fill-color: var(--text-white) !important;
            text-fill-color: var(--text-white) !important;
          }

          /* Force black text on light table rows - override global CSS */
          .performance-container table thead th {
            color: var(--text-black) !important;
            -webkit-text-fill-color: var(--text-black) !important;
            text-fill-color: var(--text-black) !important;
          }

          .performance-container table tbody tr:nth-child(even) td {
            color: var(--text-black) !important;
            -webkit-text-fill-color: var(--text-black) !important;
            text-fill-color: var(--text-black) !important;
          }

          /* Keep white text on dark rows */
          .performance-container table tbody tr:nth-child(odd) td {
            color: var(--text-white) !important;
            -webkit-text-fill-color: var(--text-white) !important;
            text-fill-color: var(--text-white) !important;
          }
        `}
      </style>

      <div className="performance-container" style={{
        margin: '0',
        width: '100%',
        maxWidth: 'none',
        backgroundColor: 'var(--dark-blue)',
        padding: '30px',
        borderRadius: '0',
        minHeight: '100vh'
      }}>
      <div className="mb-4 d-flex align-items-center" style={{ gap: '0.5rem', justifyContent: 'flex-start' }}>
        <div className="line d-none d-md-block" style={{ backgroundColor: PERFORMANCE_COLORS.WHITE, width: '40px', height: '3px' }}></div>
        <h3 style={{
          marginBottom: 0,
          color: PERFORMANCE_COLORS.WHITE,
          fontWeight: 'bold',
          textAlign: 'left'
        }}>
          <span role="img" aria-label="chart" style={{ marginRight: '8px' }}>📈</span>
          {fundName} Performance
        </h3>
      </div>

      <div style={{
        display: 'flex',
        gap: '20px',
        margin: '30px 0',
        flexWrap: 'wrap'
      }}>
        <div style={{
          flex: '1',
          minWidth: '400px',
          backgroundColor: PERFORMANCE_COLORS.DARK_BLUE,
          borderRadius: '12px',
          padding: '25px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)'
        }}>
          <h3 style={{
            color: PERFORMANCE_COLORS.WHITE,
            marginBottom: '20px',
            textAlign: 'center',
            fontWeight: 'bold'
          }}>Performance Trend</h3>
          <div style={{ position: 'relative', height: '300px' }}>
            <Line data={performanceData} options={enhancedChartOptions} />
          </div>
          <p style={{
            textAlign: 'center',
            fontSize: '0.9rem',
            color: PERFORMANCE_COLORS.WHITE,
            marginTop: '15px',
            marginBottom: 0,
            opacity: 0.8
          }}>Quarterly returns over the past 3 years</p>
        </div>

        <div style={{
          flex: '1',
          minWidth: '400px',
          backgroundColor: PERFORMANCE_COLORS.DARK_BLUE,
          borderRadius: '12px',
          padding: '25px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)'
        }}>
          <h3 style={{
            color: PERFORMANCE_COLORS.WHITE,
            marginBottom: '20px',
            textAlign: 'center',
            fontWeight: 'bold'
          }}>Annual Performance Comparison</h3>
          <div style={{ position: 'relative', height: '300px' }}>
            <Line data={annualPerformanceData} options={enhancedChartOptions} />
          </div>
          <p style={{
            textAlign: 'center',
            fontSize: '0.9rem',
            color: PERFORMANCE_COLORS.WHITE,
            marginTop: '15px',
            marginBottom: 0,
            opacity: 0.8
          }}>Benchmark returns: 10% (2021), 15% (2022), 25% (2023)</p>
        </div>
      </div>

      <div style={{
        margin: '30px auto',
        maxWidth: '900px',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }}>
        <div className="mb-4" style={{ textAlign: 'center', width: '100%' }}>
          <h3 style={{
            marginBottom: 0,
            color: PERFORMANCE_COLORS.WHITE,
            fontWeight: 'bold',
            textAlign: 'center'
          }}>
            <span role="img" aria-label="metrics" style={{ marginRight: '8px' }}>📊</span>
            Performance Metrics
          </h3>
        </div>
        <div style={{
          backgroundColor: 'var(--dark-blue)',
          borderRadius: '12px',
          padding: '20px',
          display: 'inline-block',
          margin: '0 auto'
        }}>
          <table style={{
            borderCollapse: 'collapse',
            backgroundColor: 'transparent',
            margin: '0 auto'
          }}>
            <thead>
              <tr style={{ backgroundColor: 'var(--medium-blue)' }}>
                <th style={{ padding: '12px', color: 'var(--text-black)', textAlign: 'left', borderRadius: '8px 0 0 0' }}>Metric</th>
                <th style={{ padding: '12px', color: 'var(--text-black)', textAlign: 'left' }}>Fund</th>
                <th style={{ padding: '12px', color: 'var(--text-black)', textAlign: 'left' }}>Benchmark</th>
                <th style={{ padding: '12px', color: 'var(--text-black)', textAlign: 'left', borderRadius: '0 8px 0 0' }}>Difference</th>
              </tr>
            </thead>
            <tbody>
              <tr style={{ backgroundColor: 'var(--dark-blue)', borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
                <td style={{ padding: '10px', color: 'var(--text-white)', fontWeight: '500' }}>Cumulative Return</td>
                <td style={{ padding: '10px', color: 'var(--text-white)', fontWeight: '500' }}>105.4%</td>
                <td style={{ padding: '10px', color: 'var(--text-white)', fontWeight: '500' }}>75.0%</td>
                <td style={{ padding: '10px', color: 'var(--green)', fontWeight: 'bold' }}>+30.4%</td>
              </tr>
              <tr style={{ backgroundColor: 'var(--medium-blue)', borderBottom: '1px solid rgba(0,0,0,0.1)' }}>
                <td style={{ padding: '10px', color: 'var(--text-black)', fontWeight: '500' }}>Annualized Return</td>
                <td style={{ padding: '10px', color: 'var(--text-black)', fontWeight: '500' }}>27.1%</td>
                <td style={{ padding: '10px', color: 'var(--text-black)', fontWeight: '500' }}>20.5%</td>
                <td style={{ padding: '10px', color: 'var(--green)', fontWeight: 'bold' }}>+6.6%</td>
              </tr>
              <tr style={{ backgroundColor: 'var(--dark-blue)', borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
                <td style={{ padding: '10px', color: 'var(--text-white)', fontWeight: '500' }}>Volatility (Std Dev)</td>
                <td style={{ padding: '10px', color: 'var(--text-white)', fontWeight: '500' }}>28.5%</td>
                <td style={{ padding: '10px', color: 'var(--text-white)', fontWeight: '500' }}>22.0%</td>
                <td style={{ padding: '10px', color: 'var(--red)', fontWeight: 'bold' }}>+6.5%</td>
              </tr>
              <tr style={{ backgroundColor: 'var(--medium-blue)', borderBottom: '1px solid rgba(0,0,0,0.1)' }}>
                <td style={{ padding: '10px', color: 'var(--text-black)', fontWeight: '500' }}>Sharpe Ratio</td>
                <td style={{ padding: '10px', color: 'var(--text-black)', fontWeight: '500' }}>0.95</td>
                <td style={{ padding: '10px', color: 'var(--text-black)', fontWeight: '500' }}>0.82</td>
                <td style={{ padding: '10px', color: 'var(--green)', fontWeight: 'bold' }}>+0.13</td>
              </tr>
              <tr style={{ backgroundColor: 'var(--dark-blue)' }}>
                <td style={{ padding: '10px', color: 'var(--text-white)', fontWeight: '500' }}>Maximum Drawdown</td>
                <td style={{ padding: '10px', color: 'var(--text-white)', fontWeight: '500' }}>-18.2%</td>
                <td style={{ padding: '10px', color: 'var(--text-white)', fontWeight: '500' }}>-12.5%</td>
                <td style={{ padding: '10px', color: 'var(--red)', fontWeight: 'bold' }}>-5.7%</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    </>
  );
};

export default Performance;