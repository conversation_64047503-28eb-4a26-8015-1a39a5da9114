import React from 'react';
import './fund.css';
import Tabs from '../../App/Tabs/Tabs.jsx';
import Tab from '../../App/Tabs/Tab.jsx';
import Overview from './Overview.js';
import { COLORS, SEMANTIC_COLORS } from '../../constants/colors';

import Performance from './Performance.js';
import Documents from './Documents.js';
import WhatIf from './WhatIf.js';

/*
const formatCurrency = (value) =>
  typeof value === 'number' ? `$${(value / 1_000_000).toFixed(1)}M` : 'N/A';
*/
const Fund = ({
  fundName,
  holdings = [],
  fundDetails = {},
  performanceData = null,
  annualPerformanceData = [],
  lineChartOptions = {},
  isMobile = window.innerWidth <= 768
}) => {
  return (
    <div className="fund-container fund-main-container" style={{
      marginTop: window.innerWidth <= 768 ? '80px' : '20px',
      backgroundColor: COLORS.MEDIUM_BLUE,
      minHeight: '100vh',
      margin: 0,
      padding: 0
    }}>
      {/* Fund header is now completely removed */}

      <div className="fund-content medium-blue-bg force-white-text" style={{ backgroundColor: COLORS.MEDIUM_BLUE }}>
        {/* Add a top margin to ensure tabs are at the top */}
        <div className="tabs-wrapper" style={{
          marginTop: window.innerWidth <= 768 ? '10px' : '20px',
          backgroundColor: COLORS.MEDIUM_BLUE
        }}>
          <style>
            {`
              .tabs-container {
                background-color: ${COLORS.MEDIUM_BLUE} !important;
                box-shadow: none !important;
              }
              .tabs-header {
                border-bottom: 1px solid rgba(255,255,255,0.2) !important;
                background-color: transparent !important;
              }
              .tab-button {
                color: #000000 !important;
              }
              .tab-button:hover {
                color: #000000 !important;
                background-color: rgba(0,0,0,0.1) !important;
              }
              .tab-button.active {
                color: #000000 !important;
                background-color: rgba(0,0,0,0.1) !important;
                font-weight: bold !important;
              }

              /* Fix tab underline to use frame-color instead of hardcoded blue */
              .tabs-indicator {
                background-color: var(--frame-color) !important;
              }

              .tab-button.active-tab {
                border-bottom-color: var(--frame-color) !important;
              }

              .tab-content {
                background-color: ${COLORS.MEDIUM_BLUE} !important;
              }
            `}
          </style>
          <Tabs defaultTab="Overview">
            {/* Overview Tab */}
            <Tab label="Overview" className="tab-content-centered">
              <Overview fundDetails={fundDetails} fundName={fundName} />
            </Tab>


            {/* Performance Tab */}
            <Tab label="Performance" className="tab-content-centered">
              <Performance
                annualPerformanceData={annualPerformanceData}
                fundName={fundName}
                lineChartOptions={lineChartOptions}
                performanceData={performanceData}
              />
            </Tab>

            {/* Documents Tab */}
            <Tab label="Documents" className="tab-content-centered">
              <Documents fundName={fundName} />
            </Tab>

            {/* What-If Tab */}
            <Tab label="What-If" className="tab-content-centered">
              <WhatIf
                fundName={fundName}
                lineChartOptions={lineChartOptions}
                performanceData={performanceData}
              />
            </Tab>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Fund;
