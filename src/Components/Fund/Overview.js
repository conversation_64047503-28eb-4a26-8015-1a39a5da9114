import React, { useEffect } from 'react';
import './fund.css';
import { COLORS } from '../../constants/colors';

const Overview = ({ fundDetails = {}, fundName }) => {
  // Direct reference to global colors - no copies!
  const OVERVIEW_COLORS = COLORS;



  useEffect(() => {
    return () => {
      console.log('Overview component unmounting');
    };
  }, [fundDetails, fundName]);

  return (
    <>
      {/* CSS Override to force white text on dark background */}
      <style>
        {`
          .fund-overview-content,
          .fund-overview-content *,
          .fund-overview-content h1,
          .fund-overview-content h2,
          .fund-overview-content h3,
          .fund-overview-content h4,
          .fund-overview-content h5,
          .fund-overview-content h6,
          .fund-overview-content p,
          .fund-overview-content span,
          .fund-overview-content div,
          .fund-overview-content strong {
            color: var(--text-white) !important;
            -webkit-text-fill-color: var(--text-white) !important;
            text-fill-color: var(--text-white) !important;
          }
        `}
      </style>

      <div className="fund-overview-content" style={{
        margin: '0',
        padding: '40px 60px',
        backgroundColor: 'var(--dark-blue)',
        minHeight: '100vh',
        width: '100%',
        maxWidth: '1400px',
        marginLeft: 'auto',
        marginRight: 'auto'
      }}>

        {/* Fund Title */}
        <div style={{ marginBottom: '40px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px', marginBottom: '20px' }}>
            <div style={{ backgroundColor: 'var(--frame-color)', width: '60px', height: '4px', borderRadius: '2px' }}></div>
            <h1 style={{
              margin: 0,
              color: 'var(--text-white)',
              fontSize: '2.5rem',
              fontWeight: 'bold'
            }}>
              {fundName}
            </h1>
          </div>

          <p style={{
            color: 'var(--text-white)',
            fontSize: '1.2rem',
            lineHeight: '1.6',
            margin: '0 0 0 75px',
            opacity: 0.9,
            maxWidth: '800px'
          }}>
            {fundDetails?.description || fundDetails?.strategy || 'Professional cryptocurrency fund management with focus on long-term capital appreciation and risk management.'}
          </p>
        </div>

        {/* Fund Details - Inline Flow */}
        <div style={{ marginBottom: '50px' }}>
          <h2 style={{
            color: 'var(--frame-color)',
            fontSize: '1.8rem',
            fontWeight: 'bold',
            marginBottom: '25px',
            borderBottom: '2px solid var(--frame-color)',
            paddingBottom: '10px',
            display: 'inline-block'
          }}>
            Fund Details
          </h2>

          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '40px',
            alignItems: 'baseline'
          }}>
            <div style={{ minWidth: '200px' }}>
              <span style={{ color: 'var(--text-white)', fontSize: '0.9rem', opacity: 0.8 }}>Inception Date</span>
              <div style={{ color: 'var(--text-white)', fontSize: '1.3rem', fontWeight: '600', marginTop: '5px' }}>
                {fundDetails?.start ? new Date(fundDetails.start).toLocaleDateString() : 'N/A'}
              </div>
            </div>
            <div style={{ minWidth: '200px' }}>
              <span style={{ color: 'var(--text-white)', fontSize: '0.9rem', opacity: 0.8 }}>Fund Manager</span>
              <div style={{ color: 'var(--text-white)', fontSize: '1.3rem', fontWeight: '600', marginTop: '5px' }}>
                {fundDetails?.manager || 'Moolah Capital'}
              </div>
            </div>
            <div style={{ minWidth: '200px' }}>
              <span style={{ color: 'var(--text-white)', fontSize: '0.9rem', opacity: 0.8 }}>Minimum Investment</span>
              <div style={{ color: 'var(--text-white)', fontSize: '1.3rem', fontWeight: '600', marginTop: '5px' }}>
                {fundDetails?.minimumInvestment || '$10,000'}
              </div>
            </div>
            <div style={{ minWidth: '150px' }}>
              <span style={{ color: 'var(--text-white)', fontSize: '0.9rem', opacity: 0.8 }}>Currency</span>
              <div style={{ color: 'var(--text-white)', fontSize: '1.3rem', fontWeight: '600', marginTop: '5px' }}>
                {fundDetails?.currency || 'USD'}
              </div>
            </div>
          </div>
        </div>

        {/* Key Metrics - Horizontal Flow */}
        <div style={{ marginBottom: '50px' }}>
          <h2 style={{
            color: 'var(--frame-color)',
            fontSize: '1.8rem',
            fontWeight: 'bold',
            marginBottom: '30px',
            borderBottom: '2px solid var(--frame-color)',
            paddingBottom: '10px',
            display: 'inline-block'
          }}>
            Key Performance Metrics
          </h2>

          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '60px',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <div style={{ textAlign: 'center', minWidth: '180px' }}>
              <div style={{ color: 'var(--text-white)', fontSize: '0.9rem', marginBottom: '8px', opacity: 0.8 }}>1-Year Performance</div>
              <div style={{
                color: fundDetails?.performance?.oneYear >= 0 ? 'var(--frame-color)' : 'var(--red)',
                fontSize: '2rem',
                fontWeight: 'bold',
                lineHeight: '1'
              }}>
                {fundDetails?.performance?.oneYear ? `${fundDetails.performance.oneYear}%` : 'N/A'}
              </div>
            </div>

            <div style={{ textAlign: 'center', minWidth: '180px' }}>
              <div style={{ color: 'var(--text-white)', fontSize: '0.9rem', marginBottom: '8px', opacity: 0.8 }}>Management Fee</div>
              <div style={{ color: 'var(--text-white)', fontSize: '2rem', fontWeight: 'bold', lineHeight: '1' }}>
                {typeof fundDetails?.Fee === 'number' ? `${fundDetails.Fee}%` : fundDetails?.Fee || 'N/A'}
              </div>
            </div>

            <div style={{ textAlign: 'center', minWidth: '180px' }}>
              <div style={{ color: 'var(--text-white)', fontSize: '0.9rem', marginBottom: '8px', opacity: 0.8 }}>Assets Under Management</div>
              <div style={{ color: 'var(--text-white)', fontSize: '2rem', fontWeight: 'bold', lineHeight: '1' }}>
                {typeof fundDetails?.aum === 'number' ? `$${fundDetails.aum}M` : fundDetails?.aum || 'N/A'}
              </div>
            </div>
          </div>
        </div>

        {/* Investment Strategy & Objective - Side by Side */}
        <div style={{ marginBottom: '50px' }}>
          <h2 style={{
            color: 'var(--frame-color)',
            fontSize: '1.8rem',
            fontWeight: 'bold',
            marginBottom: '30px',
            borderBottom: '2px solid var(--frame-color)',
            paddingBottom: '10px',
            display: 'inline-block'
          }}>
            Investment Approach
          </h2>

          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '60px',
            alignItems: 'start'
          }}>
            <div>
              <h3 style={{
                color: 'var(--text-white)',
                fontSize: '1.4rem',
                fontWeight: '600',
                marginBottom: '20px',
                borderLeft: '4px solid var(--frame-color)',
                paddingLeft: '20px'
              }}>
                Strategy
              </h3>
              <p style={{
                color: 'var(--text-white)',
                lineHeight: '1.7',
                fontSize: '1.1rem',
                margin: 0,
                opacity: 0.9
              }}>
                {fundDetails?.strategy || 'Advanced cryptocurrency portfolio management utilizing quantitative analysis and risk management techniques to optimize returns while managing downside risk.'}
              </p>
            </div>

            <div>
              <h3 style={{
                color: 'var(--text-white)',
                fontSize: '1.4rem',
                fontWeight: '600',
                marginBottom: '20px',
                borderLeft: '4px solid var(--frame-color)',
                paddingLeft: '20px'
              }}>
                Objective
              </h3>
              <p style={{
                color: 'var(--text-white)',
                lineHeight: '1.7',
                fontSize: '1.1rem',
                margin: 0,
                opacity: 0.9
              }}>
                {fundDetails?.investmentObjective || 'Long-term capital appreciation through strategic cryptocurrency investments while maintaining appropriate risk management protocols.'}
              </p>
            </div>
          </div>
        </div>

        {/* Risk Assessment - Integrated */}
        <div style={{ marginBottom: '40px' }}>
          <h2 style={{
            color: 'var(--frame-color)',
            fontSize: '1.8rem',
            fontWeight: 'bold',
            marginBottom: '30px',
            borderBottom: '2px solid var(--frame-color)',
            paddingBottom: '10px',
            display: 'inline-block'
          }}>
            Risk Profile
          </h2>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '40px',
            flexWrap: 'wrap'
          }}>
            <div style={{ flex: '1', minWidth: '400px' }}>
              {/* Risk Level Line */}
              <div style={{
                position: 'relative',
                height: '12px',
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: '6px',
                marginBottom: '15px',
                overflow: 'hidden'
              }}>
                {/* Background gradient */}
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(to right, #28a745 0%, #ffc107 50%, #dc3545 100%)',
                  borderRadius: '6px'
                }}></div>

                {/* Risk indicator dot */}
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: fundDetails?.riskLevel?.toLowerCase() === 'low' ? '20%' :
                        fundDetails?.riskLevel?.toLowerCase() === 'high' ? '80%' : '50%',
                  transform: 'translate(-50%, -50%)',
                  width: '20px',
                  height: '20px',
                  backgroundColor: 'var(--white)',
                  borderRadius: '50%',
                  border: '2px solid var(--dark-blue)',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
                }}></div>
              </div>

              {/* Risk labels */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '0'
              }}>
                <span style={{ color: 'var(--text-white)', fontSize: '0.9rem', opacity: 0.8 }}>Low Risk</span>
                <span style={{ color: 'var(--text-white)', fontSize: '0.9rem', opacity: 0.8 }}>Medium Risk</span>
                <span style={{ color: 'var(--text-white)', fontSize: '0.9rem', opacity: 0.8 }}>High Risk</span>
              </div>
            </div>

            <div style={{ textAlign: 'center', minWidth: '200px' }}>
              <div style={{ color: 'var(--text-white)', fontSize: '0.9rem', marginBottom: '8px', opacity: 0.8 }}>Current Risk Level</div>
              <div style={{
                fontSize: '1.4rem',
                fontWeight: 'bold',
                color: fundDetails?.riskLevel?.toLowerCase() === 'high' ? 'var(--red)' :
                      fundDetails?.riskLevel?.toLowerCase() === 'medium' ? 'var(--yellow)' : 'var(--green)'
              }}>
                {fundDetails?.riskLevel ? fundDetails.riskLevel.charAt(0).toUpperCase() + fundDetails.riskLevel.slice(1) : 'Medium'}
              </div>
            </div>
          </div>
        </div>


    </div>
    </>
  );
};

export default Overview;