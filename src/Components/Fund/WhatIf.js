import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import './fund.css';
import { COLORS } from '../../constants/colors';

const WhatIf = ({ fundName, performanceData, lineChartOptions }) => {
  // Direct reference to global colors - no copies!
  const WHATIF_COLORS = COLORS;
  // State for investment amount with default of $10,000
  const [investmentAmount, setInvestmentAmount] = useState(10000);
  // State for formatted display of the investment amount
  const [formattedAmount, setFormattedAmount] = useState('$10,000');
  // State for cumulative returns
  const [cumulativeReturns, setCumulativeReturns] = useState([]);
  // State for chart data
  const [whatIfData, setWhatIfData] = useState(null);

  // Calculate returns when investment amount changes
  useEffect(() => {

    // Check if performance data exists and has the expected structure
    if (!performanceData || !performanceData.datasets || !performanceData.datasets[0] || !performanceData.datasets[0].data) {
      console.error('Performance data is missing or has invalid structure:', performanceData);
      return;
    }

    // Calculate cumulative returns based on monthly performance data
    const newCumulativeReturns = performanceData.datasets[0].data.reduce((acc, monthlyReturn, index) => {
      // Convert the monthly percentage to a decimal (e.g., 5% becomes 0.05)
      const monthlyReturnDecimal = monthlyReturn / 100;

      if (index === 0) {
        // First month: initial investment + first month return
        return [...acc, investmentAmount * (1 + monthlyReturnDecimal)];
      } else {
        // Subsequent months: previous value + current month return
        const previousValue = acc[index - 1];
        return [...acc, previousValue * (1 + monthlyReturnDecimal)];
      }
    }, []);

    setCumulativeReturns(newCumulativeReturns);

    // Create data for the what-if chart
    const newWhatIfData = {
      labels: performanceData.labels,
      datasets: [
        {
          label: `$${investmentAmount.toLocaleString()} Investment Growth`,
          data: newCumulativeReturns,
          borderColor: '#4BC0C0',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderWidth: 2,
          pointRadius: 3,
          tension: 0.3,
          fill: true
        }
      ]
    };

    setWhatIfData(newWhatIfData);

    // Format the investment amount for display
    setFormattedAmount('$' + investmentAmount.toLocaleString());
  }, [investmentAmount, performanceData]);

  // Handle slider change
  const handleSliderChange = (e) => {
    setInvestmentAmount(Number(e.target.value));
  };

  // Enhanced chart options with white axes text (same as Performance charts)
  const enhancedChartOptions = {
    ...lineChartOptions,
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      ...lineChartOptions?.plugins,
      legend: {
        ...lineChartOptions?.plugins?.legend,
        labels: {
          ...lineChartOptions?.plugins?.legend?.labels,
          color: WHATIF_COLORS.WHITE
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': $' + context.parsed.y.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          }
        }
      }
    },
    scales: {
      x: {
        ...lineChartOptions?.scales?.x,
        ticks: {
          ...lineChartOptions?.scales?.x?.ticks,
          color: WHATIF_COLORS.WHITE
        },
        grid: {
          ...lineChartOptions?.scales?.x?.grid,
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      y: {
        ...lineChartOptions?.scales?.y,
        ticks: {
          ...lineChartOptions?.scales?.y?.ticks,
          color: WHATIF_COLORS.WHITE,
          callback: function(value) {
            return '$' + value.toLocaleString();
          }
        },
        title: {
          display: true,
          text: 'Value ($)',
          color: WHATIF_COLORS.WHITE
        },
        grid: {
          ...lineChartOptions?.scales?.y?.grid,
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    }
  };

  // Calculate final value and total return
  const finalValue = cumulativeReturns.length > 0 ? cumulativeReturns[cumulativeReturns.length - 1] : 0;
  const totalReturn = investmentAmount > 0 ? ((finalValue - investmentAmount) / investmentAmount) * 100 : 0;
  const totalGain = finalValue - investmentAmount;

  return (
    <>
      {/* CSS Override for What-If page */}
      <style>
        {`
          /* Fix title text to use global dark variable */
          .whatif-container h3 {
            color: var(--text-black) !important;
            -webkit-text-fill-color: var(--text-black) !important;
            text-fill-color: var(--text-black) !important;
          }

          /* Fix slider thumb color to use frame-color */
          .whatif-container input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--frame-color) !important;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }

          .whatif-container input[type="range"]::-moz-range-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--frame-color) !important;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }
        `}
      </style>

      <div className="whatif-container" style={{ padding: '30px', backgroundColor: 'var(--medium-blue)', color: 'var(--text-white)' }}>
        <h3 style={{
          color: 'var(--text-black)',
          fontWeight: 'bold',
          textAlign: 'center',
          marginBottom: '20px'
        }}>{fundName} performance simulation</h3>

      <div style={{
        backgroundColor: WHATIF_COLORS.DARK_BLUE,
        borderRadius: '12px',
        padding: '25px',
        margin: '20px 0',
        border: '1px solid rgba(255, 255, 255, 0.1)'
      }}>
        <label style={{
          display: 'block',
          textAlign: 'left',
          color: WHATIF_COLORS.WHITE,
          fontWeight: 'bold',
          marginBottom: '15px',
          fontSize: '1.1rem'
        }} htmlFor="investment-slider">Investment Amount: ${investmentAmount.toLocaleString()}</label>
        <input
          type="range"
          id="investment-slider"
          className="investment-slider"
          min="10000"
          max="1000000"
          step="10000"
          value={investmentAmount}
          onChange={handleSliderChange}
          style={{
            width: '100%',
            height: '8px',
            borderRadius: '5px',
            background: `linear-gradient(to right, ${WHATIF_COLORS.MEDIUM_BLUE} 0%, ${WHATIF_COLORS.MEDIUM_BLUE} ${((investmentAmount - 10000) / (1000000 - 10000)) * 100}%, #ddd ${((investmentAmount - 10000) / (1000000 - 10000)) * 100}%, #ddd 100%)`,
            outline: 'none',
            cursor: 'pointer'
          }}
        />
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        margin: '30px 0'
      }}>
        <div style={{
          backgroundColor: WHATIF_COLORS.DARK_BLUE,
          color: WHATIF_COLORS.WHITE,
          borderRadius: '12px',
          padding: '20px',
          textAlign: 'center',
          border: '1px solid rgba(255, 255, 255, 0.1)'
        }}>
          <h4 style={{ color: WHATIF_COLORS.WHITE, marginBottom: '10px', fontWeight: 'bold' }}>Initial Investment</h4>
          <p style={{ fontSize: '1.5rem', fontWeight: 'bold', margin: 0, color: WHATIF_COLORS.WHITE }}>${investmentAmount.toLocaleString()}</p>
        </div>
        <div style={{
          backgroundColor: WHATIF_COLORS.DARK_BLUE,
          color: WHATIF_COLORS.WHITE,
          borderRadius: '12px',
          padding: '20px',
          textAlign: 'center',
          border: '1px solid rgba(255, 255, 255, 0.1)'
        }}>
          <h4 style={{ color: WHATIF_COLORS.WHITE, marginBottom: '10px', fontWeight: 'bold' }}>Current Value</h4>
          <p style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            margin: 0,
            color: finalValue >= investmentAmount ? '#4caf50' : '#f44336'
          }}>
            ${finalValue.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            })}
          </p>
        </div>
        <div style={{
          backgroundColor: WHATIF_COLORS.DARK_BLUE,
          color: WHATIF_COLORS.WHITE,
          borderRadius: '12px',
          padding: '20px',
          textAlign: 'center',
          border: '1px solid rgba(255, 255, 255, 0.1)'
        }}>
          <h4 style={{ color: WHATIF_COLORS.WHITE, marginBottom: '10px', fontWeight: 'bold' }}>Total Gain</h4>
          <p style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            margin: 0,
            color: totalGain >= 0 ? '#4caf50' : '#f44336'
          }}>
            ${totalGain.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            })}
          </p>
        </div>
        <div style={{
          backgroundColor: WHATIF_COLORS.DARK_BLUE,
          color: WHATIF_COLORS.WHITE,
          borderRadius: '12px',
          padding: '20px',
          textAlign: 'center',
          border: '1px solid rgba(255, 255, 255, 0.1)'
        }}>
          <h4 style={{ color: WHATIF_COLORS.WHITE, marginBottom: '10px', fontWeight: 'bold' }}>Total Return</h4>
          <p style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            margin: 0,
            color: totalReturn >= 0 ? '#4caf50' : '#f44336'
          }}>
            {totalReturn.toFixed(2)}%
          </p>
        </div>
      </div>
      <div style={{
        backgroundColor: WHATIF_COLORS.DARK_BLUE,
        borderRadius: '12px',
        padding: '25px',
        margin: '30px 0',
        border: '1px solid rgba(255, 255, 255, 0.1)'
      }}>
        <h3 style={{
          color: WHATIF_COLORS.WHITE,
          marginBottom: '20px',
          textAlign: 'center',
          fontWeight: 'bold'
        }}>Investment Growth Over Time</h3>
        <div style={{ position: 'relative', height: '300px' }}>
          {whatIfData && <Line data={whatIfData} options={enhancedChartOptions} />}
        </div>
        <p style={{
          textAlign: 'center',
          fontSize: '0.9rem',
          color: WHATIF_COLORS.WHITE,
          marginTop: '15px',
          marginBottom: 0,
          opacity: 0.8
        }}>Monthly growth of a {formattedAmount} investment over the past 3 years</p>
      </div>
    </div>
    </>
  );
};

export default WhatIf;
