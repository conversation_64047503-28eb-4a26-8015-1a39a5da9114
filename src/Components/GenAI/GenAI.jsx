import React from 'react';
import 'bootstrap/dist/js/bootstrap.bundle.min';
import '../../App.css';
import CTASection from '../../App/CTA/CTASection';
import FullHeightHero from '../Common/FullHeightHero';
import { COLORS, SEMANTIC_COLORS } from '../../constants/colors';
import Section<PERSON>rame from '../Common/SectionFrame';
import SectionFrame2 from '../Common/SectionFrame2';

const GenAI = () => {
	const risk_strategies = [
		{
			id: "Moolah-Approach",
			image: "/AI image1.jpg",
			title: "Automated Research and Idea Generation",
			text: `<p>Generative AI streamlines our research pipeline by ingesting and processing large datasets, including price feeds, order book depth, blockchain transaction flows, sentiment metrics, and macroeconomic indicators. Using natural language processing (NLP) and large language models (LLMs), we scan news sources, whitepapers, governance proposals, and social channels to detect early signals of market-moving events.</p>
			<p>This automation shifts the workload from manual data gathering to higher-value activities like strategy calibration, risk scenario modelling, and portfolio optimisation.</p>
			<p>These AI-driven research capabilities are embedded into the AlphaGlobal GenAI LLM Funds for customised portfolio creation and also enhance tactical positioning in the AlphaGlobal Special Situations Fund.</p>`
		},
		{
			id: "Moolah-Efficiency",
			title: "Enhanced Portfolio Construction",
			image: "/risk-riskreward.jpg",
			text: `<p>AI-driven portfolio optimisation models process multidimensional data sets — including price history, volatility surfaces, correlation matrices, liquidity profiles, and on-chain activity — to identify the most efficient asset mixes for a given risk target. Factor analysis and regime detection algorithms help adjust weightings based on prevailing market conditions, such as bullish momentum phases or liquidity contractions.</p>
			<p>These models dynamically adapt as market structures evolve, ensuring diversification remains effective under changing volatility regimes.</p>
			<p>This approach underpins the allocation process in the AlphaGlobal Market Index Fund, AlphaGlobal Momentum Fund, and AlphaGlobal DeFi Leaders Fund, while also supporting the targeted positioning of the AlphaGlobal Special Situations Fund.</p>`
			},
		{
			id: "Moolah-Speed",
			title: "Real-Time Market Response",
			image: "/risk-riskreward.jpg",
			text: `<p>Large language models (LLMs) and real-time analytics pipelines process and interpret market-moving events — from breaking news and regulatory announcements to shifts in sentiment detected on social platforms and blockchain governance forums. Using natural language processing (NLP) and entity recognition, our systems classify events by relevance, market impact probability, and historical response patterns.</p>
			<p>By integrating automated alerting and scenario modelling, we can respond to both anticipated catalysts and unexpected shocks with speed and precision.</p>
			<p>These real-time adaptive capabilities are a cornerstone of the AlphaGlobal GenAI LLM Funds and play a critical role in the tactical overlays of the AlphaGlobal Special Situations Fund.</p>`
		},
		{
			id: "Moolah-Cost",
			title: "Cost Reduction",
			image: "/risk-riskreward.jpg",
			text: `<p>Generative AI automates labour-intensive tasks such as market data aggregation, sentiment analysis, transaction pattern detection, and strategy backtesting. By integrating APIs with exchanges, DeFi protocols, and data providers, we minimise operational overhead and shorten time-to-market for new fund strategies.</p>
			<p>These efficiencies lower execution costs, reduce human error, and allow resources to be redirected toward innovation and risk oversight — benefits that compound over time for investors.</p>
			<p>This cost-efficient infrastructure supports all AlphaGlobal strategies, from the Market Index Fund and Momentum Fund to the DeFi Leaders and Special Situations Funds, with the most advanced automation powering the GenAI LLM Funds.</p>`
		}
	];

	return (
		<div className='text-black'>
			<FullHeightHero
				backgroundVideo="/funds-background.mp4"
				posterImage="/loading-strategies.jpg"
				overlayColor="rgba(0, 0, 0, 0.5)"
				videoPlaybackRate={0.3}
			>
				<div className="container text-center">
					<h1 className="display-4 text-white mb-4">GenAI Strategies</h1>
					<p className="lead text-white mb-5">AI-powered investment solutions</p>
				</div>
			</FullHeightHero>

			{/* Section Tagline */}
			<div className="full-width-medium-section" style={{
				backgroundColor: COLORS.MEDIUM_BLUE,
				paddingTop: '60px',
				paddingBottom: '60px',
				paddingLeft: '0',
				paddingRight: '0',
				minHeight: '500px',
				display: 'flex',
				alignItems: 'center',
				color: 'var(--text-medium-blue)'
			}}>
				<div className="container" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
					<div className="row">
						<div className="col-lg-7 text-lg-start text-center">
							<h2 className="h2 ms-4" style={{ color: 'var(--text-medium-blue)' }}>
								<span style={{ fontSize: '1.5em' }}>
									Pioneering LLM Technology in Crypto Asset Management.
								</span>
							</h2>
							<p className="body-text ms-4 ms-lg-4" style={{ color: 'var(--text-medium-blue)' }}>
							At Moolah Capital, we integrate generative AI and large language models into our investment process to enhance research capabilities, improve portfolio construction, and enable more responsive market analysis.
							</p>
						</div>
					</div>
				</div>
			</div>
			<SectionFrame />

			{/* CTA Section #1 */}
			<section id='genai-cta1' className="full-width-dark-section" style={{ backgroundColor: COLORS.DARK_BLUE, paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0' }}>
				<div className="container text-center-mbl text-white" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
					<CTASection
						title="Start Your Financial Journey"
						cta="Sign up"
						link="/signup#account-signup"
						theme='signup'
						titleStyle={{ color: 'white' }}
					/>
				</div>
			</section>
			{/* Strategy Sections */}
			<div>
				{risk_strategies.map((strategy, index) => (
					<section key={strategy.id} className={index % 2 === 0 ? 'full-width-medium-section' : 'full-width-dark-section'} style={{ backgroundColor: index % 2 === 0 ? COLORS.MEDIUM_BLUE : COLORS.DARK_BLUE, minHeight: '500px', paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center', color: index % 2 === 0 ? 'var(--text-medium-blue)' : 'var(--text-white)' }}>
						<div className={`container-fluid`} style={{ color: index % 2 === 0 ? 'var(--text-medium-blue)' : 'var(--text-white)', paddingLeft: '20px', paddingRight: '20px' }}>
							<div className="row g-5">
								<div className={`col-lg-5 my-4 ${index % 2 === 0 ? 'order-lg-1' : 'order-lg-2'}`}>
									<h2 className="h2 text-start mb-4 mobile-header">{strategy.title}</h2>
									<div className="body-text text-start" dangerouslySetInnerHTML={{ __html: strategy.text }}></div>
								</div>
								<div className={`col-lg-7 my-4 d-flex justify-content-center align-items-center ${index % 2 === 0 ? 'order-lg-2' : 'order-lg-1'}`}>
									<div className="fund-image-container" style={{ maxWidth: '600px', width: '100%' }}>
										<img
											src={strategy.image}
											alt={strategy.title}
											className='fund-image'
											style={{ maxWidth: '100%', maxHeight: '450px', objectFit: 'cover', width: '100%', borderRadius: '12px' }}
											onError={(e) => {
												e.target.onerror = null;
												e.target.src = "/placeholder.jpg";
											}}
										/>
									</div>
								</div>
							</div>
						</div>
					</section>
				))}
			</div>

			{/* CTA Section */}
			<SectionFrame shape="curve" />
			<section className="full-width-dark-section" style={{ backgroundColor: COLORS.DARK_BLUE, paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0' }}>
				<div className="container text-center-mbl text-white" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
					<CTASection
						title="Explore AI-Powered Investment Strategies"
						cta="Learn More"
						link="/learn?topic=genai"
						theme='learn'
						titleStyle={{ color: 'white' }}
					/>
				</div>
			</section>
			<SectionFrame2 shape="zigzag" />
		</div>
	);
}

export default GenAI;
