// Funds.jsx

import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import CTASection from '../../App/CTA/CTASection.jsx';
import FullHeightHero from '../Common/FullHeightHero';
import Section<PERSON>rame from '../Common/SectionFrame';
import SectionFrame2 from '../Common/SectionFrame2';

// Import global colors
import { COLORS } from '../../constants/colors';

// Use global colors directly
const FUNDS_COLORS = COLORS;

const Funds = () => {
  const routerLocation = useLocation();

  // Improved hash navigation handling
  useEffect(() => {
    // First scroll to top to ensure consistent positioning
    window.scrollTo(0, 0);

    // Then handle hash navigation with a slight delay to ensure DOM is ready
    if (routerLocation.hash) {
      // Use setTimeout to ensure the component is fully rendered
      setTimeout(() => {
        const id = routerLocation.hash.slice(1); // Remove the # character
        const element = document.getElementById(id);

        if (element) {
          // Calculate position with further adjusted navbar offset
          const navbarHeight = 80; // Reduced from 90px to 80px
          const elementPosition = element.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.pageYOffset - navbarHeight;

          // Scroll to the element
          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });
        } else {
          console.warn(`Element with id "${id}" not found`);
        }
      }, 300);
    }
  }, [routerLocation]);

  return (
    <div>
      {/* Section 1 - Hero */}
      <FullHeightHero
        backgroundVideo="/iStock-2160279068.mp4"
        posterImage="/loading-funds.jpg"
        overlayColor="rgba(0, 0, 0, 0.4)"
        videoPlaybackRate={0.3}
      >
        <div className="container text-center">
          <h1 className="display-4 text-white mb-4 mobile-header">Our Investment Funds</h1>
          <p className="lead text-white mb-5">Diversified crypto investment solutions for every strategy</p>
        </div>
      </FullHeightHero>

      {/* Content Container */}
      <div className="page-content-container" id="funds-content">
        {/* Section 2 - Funds Intro */}
        <section className="full-width-dark-section" style={{ backgroundColor: FUNDS_COLORS.DARK_BLUE, minHeight: '500px', paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center' }}>
        <div className="container-fluid text-white" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
          <div className='row'>
            <div className='col-lg-6 my-3'>
              <h2 className='h2 text-start mb-4 mobile-header' style={{ marginTop: '0' }}>Moolah Capital Passive Funds</h2>
              <p className='body-text text-start'>Passive index funds aim to match the performance of a market index — like the S&P 500 or a crypto market index — instead of trying to beat it. They do this by holding the same assets, in the same proportions, as the index they follow.</p>
              <p className='body-text text-start'>The <strong>Moolah Capital AlphaGlobal Market Index Fund</strong> is Moolah Capital's fund in this category, giving investors simple and cost-effective exposure to the overall crypto market.</p>
              <p className='body-text text-start'>These funds are:</p>
              <ul className='body-text text-start'>
                <li><span style={{ fontWeight: 'bold' }}>Low cost</span> – minimal trading and no active management keep fees down.</li>
                <li><span style={{ fontWeight: 'bold' }}>Diversified</span> – tracking a broad index spreads risk across many assets.</li>
                <li><span style={{ fontWeight: 'bold' }}>Transparent</span> – holdings closely match a publicly available index.</li>
                <li><span style={{ fontWeight: 'bold' }}>Consistent</span> – performance moves in line with the market over time.</li>
              </ul>
            </div>
            <div className='col-lg-6 my-3 d-flex justify-content-center align-items-center' style={{ padding: '20px' }}>
              <div className="fund-image-container" style={{ width: '100%', maxWidth: '600px' }}>
                <img
                  src={'/funds-smartbeta.jpg'}
                  alt='passive funds'
                  className='fund-image'
                  style={{ width: '100%', height: 'auto', minHeight: '450px', objectFit: 'cover', borderRadius: '12px' }}
                  onError={(e) => {
                    e.target.onerror = null; // Prevent infinite loop
                    e.target.src = "/placeholder.jpg";
                  }}
                />
              </div>
            </div>
          </div>
          {/* CTA Section - Full Width */}
          <div className="d-flex justify-content-center align-items-center my-4 text-center-mbl" style={{ minHeight: '150px', width: '100%' }}>
            <div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
              <CTASection
                title="Start Your Financial Journey"
                cta="Sign up"
                link="/signup#account-signup"
                theme='signup'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-dark-section"
              />
              <CTASection
                title="Request Info about this Fund"
                cta="Learn More"
                link="/learn?topic=smartbeta"
                theme='learn'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-dark-section"
              />
            </div>
          </div>
        </div>
      </section>

         {/* Section 5 - Smart Beta Funds */}
         <section id="smartbeta" className='position-relative overflow-hidden full-width-medium-section' style={{ backgroundColor: FUNDS_COLORS.MEDIUM_BLUE, minHeight: '500px', paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center', color: 'var(--text-medium-blue)' }}>
        <div className="container-fluid" style={{ color: 'var(--text-medium-blue)', paddingLeft: '20px', paddingRight: '20px' }}>
          <div className='row'>
            <div className='col-lg-6 my-3 order-lg-2'>
              <h2 className='h2 text-start mb-4 mobile-header' style={{ marginTop: '0' }}>Moolah Capital Smart Beta Funds</h2>
              <p className='body-text text-start'>Smart beta funds use clear, rules-based methods to choose and weight assets based on factors that may improve returns or lower risk — such as liquidity, volatility, on-chain activity, or network growth.</p>
              <p className='body-text text-start'>The <strong>Moolah Capital AlphaGlobal Momentum Fund</strong> is Moolah Capital’s smart beta fund in this category, focusing on assets with strong upward trends.</p>
              <p className='body-text text-start'>These strategies aim to beat traditional indices or offer better risk-adjusted returns, while keeping costs lower than active funds, though slightly higher than standard passive funds.</p>
              <p className='body-text text-start'>Our smart beta crypto funds publish their rules for full transparency and apply filters to help reduce downside risk.</p>
            </div>
            <div className='col-lg-6 my-3 d-flex justify-content-center align-items-center order-lg-1' style={{ padding: '20px' }}>
              <div className="fund-image-container" style={{ width: '100%', maxWidth: '600px' }}>
                <img
                  src='/funds-smartbeta.jpg'
                  alt='smart beta'
                  className='fund-image'
                  style={{ width: '100%', height: 'auto', minHeight: '450px', objectFit: 'cover', borderRadius: '12px' }}
                  onError={(e) => {
                    e.target.onerror = null; // Prevent infinite loop
                    e.target.src = "/placeholder.jpg";
                  }}
                />
              </div>
            </div>
          </div>
          {/* CTA Section - Full Width */}
          <div className="d-flex justify-content-center align-items-center my-4 text-center-mbl" style={{ minHeight: '150px', width: '100%' }}>
            <div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
              <CTASection
                title="Start Your Financial Journey"
                cta="Sign up"
                link="/signup#account-signup"
                theme='signup'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-blue-section"
              />
              <CTASection
                title="Request Info about this Fund"
                cta="Learn More"
                link="/learn?topic=smartbeta"
                theme='learn'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-blue-section"
              />
            </div>
          </div>
        </div>
      </section>

      <SectionFrame shape="circles" />
      <section id="funds-intro" className='position-relative text-white overflow-hidden full-width-dark-section' style={{ backgroundColor: FUNDS_COLORS.DARK_BLUE, minHeight: '500px', paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center' }}>
        <div className="container-fluid text-white" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
          <div className='row'>
            <div className='col-lg-6 my-3'>
              <h2 className='h2 text-start mb-4 mobile-header' style={{ marginTop: '0' }}>Moolah Capital DeFi Funds</h2>
              <p className='body-text text-start'>The <strong>AlphaGlobal DeFi Leaders Fund</strong> provides strategic exposure to the most innovative and resilient projects in the decentralised finance ecosystem. Our investment approach focuses on established DeFi protocols with:</p>
              <ul className='body-text text-start'>
               <li><span style={{ fontWeight: 'bold' }}>Strong </span> market leadership in lending, trading, derivatives, and liquidity solutions.</li>
               <li><span style={{ fontWeight: 'bold' }}>Robust</span> security frameworks with audited smart contracts and proven operational resilience.</li>
               <li><span style={{ fontWeight: 'bold' }}>Sustainable</span> tokenomics and revenue-generating business models.</li>
               <li><span style={{ fontWeight: 'bold' }}>High</span> user adoption and network activity, signalling long-term growth potential.</li>
              </ul>
              <p className='body-text text-start'>DeFi has emerged as one of the fastest-growing segments in digital assets, reshaping how individuals and institutions access financial services. By concentrating on industry leaders, the fund aims to deliver diversified exposure to the sector while managing the inherent risks of emerging technologies.</p>
            </div>
            <div className='col-lg-6 my-3 d-flex justify-content-center align-items-center' style={{ padding: '20px' }}>
              <div className="fund-image-container" style={{ width: '100%', maxWidth: '600px' }}>
                <img
                  src={'/funds-smartbeta.jpg'}
                  alt='passive funds'
                  className='fund-image'
                  style={{ width: '100%', height: 'auto', minHeight: '450px', objectFit: 'cover', borderRadius: '12px' }}
                  onError={(e) => {
                    e.target.onerror = null; // Prevent infinite loop
                    e.target.src = "/placeholder.jpg";
                  }}
                />
              </div>
            </div>
          </div>
          {/* CTA Section - Full Width */}
          <div className="d-flex justify-content-center align-items-center my-4 text-center-mbl" style={{ minHeight: '150px', width: '100%' }}>
            <div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
              <CTASection
                title="Start Your Financial Journey"
                cta="Sign up"
                link="/signup#account-signup"
                theme='signup'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-dark-section"
              />
              <CTASection
                title="Request Info about this Fund"
                cta="Learn More"
                link="/learn?topic=smartbeta"
                theme='learn'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-dark-section"
              />
            </div>
          </div>
        </div>
      </section>

          {/* Section 5 - Special Situations Funds */}
          <section id="special" className='position-relative overflow-hidden full-width-medium-section' style={{ backgroundColor: FUNDS_COLORS.MEDIUM_BLUE, minHeight: '500px', paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center', color: 'var(--text-medium-blue)' }}>
        <div className="container-fluid" style={{ color: 'var(--text-medium-blue)', paddingLeft: '20px', paddingRight: '20px' }}>
          <div className='row'>
            <div className='col-lg-6 my-3 order-lg-2'>
              <h2 className='h2 text-start mb-4 mobile-header' style={{ marginTop: '0' }}>Moolah Capital Special Situations Funds</h2>
              <p className='body-text text-start'>The <strong>Moolah Capital AlphaGlobal Special Situations Fund</strong> is event-driven and opportunistic, focusing on tactical, time-sensitive trades. Positions are typically held only for the duration of a particular event or until the value is realised.</p>
                <p className='body-text text-start'>Examples of opportunities include:</p>
                <ul className='body-text text-start'>
                 <li><span style={{ fontWeight: 'bold' }}>Token</span> restructurings or governance transitions.</li>
                 <li><span style={{ fontWeight: 'bold' }}>Distressed</span> projects with recovery potential.</li>
                 <li><span style={{ fontWeight: 'bold' }}>Token</span> unlock events or blockchain forks.</li>
                 <li><span style={{ fontWeight: 'bold' }}>Liquidity</span> provisioning linked to protocol upgrades.</li>
                 <li><span style={{ fontWeight: 'bold' }}>Litigation-driven</span> trades or quantitative arbitrage.</li>
                  <li><span style={{ fontWeight: 'bold' }}>DeFi yield</span> strategies or selective early-stage token venture.</li>
                </ul>
                <p className='body-text text-start'>By pursuing targeted opportunities with asymmetric upside potential, the Special Situations Fund aims to deliver returns that move independently from broader crypto market trends.</p>
            </div>
            <div className='col-lg-6 my-3 d-flex justify-content-center align-items-center order-lg-1' style={{ padding: '20px' }}>
              <div className="fund-image-container" style={{ width: '100%', maxWidth: '600px' }}>
                <img
                  src='/funds-smartbeta.jpg'
                  alt='smart beta'
                  className='fund-image'
                  style={{ width: '100%', height: 'auto', minHeight: '450px', objectFit: 'cover', borderRadius: '12px' }}
                  onError={(e) => {
                    e.target.onerror = null; // Prevent infinite loop
                    e.target.src = "/placeholder.jpg";
                  }}
                />
              </div>
            </div>
          </div>
          {/* CTA Section - Full Width */}
          <div className="d-flex justify-content-center align-items-center my-4 text-center-mbl" style={{ minHeight: '150px', width: '100%' }}>
            <div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
              <CTASection
                title="Start Your Financial Journey"
                cta="Sign up"
                link="/signup#account-signup"
                theme='signup'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-blue-section"
              />
              <CTASection
                title="Request Info about this Fund"
                cta="Learn More"
                link="/learn?topic=smartbeta"
                theme='learn'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-blue-section"
              />
            </div>
          </div>
        </div>
      </section>

      <section id="genai" className='position-relative text-white overflow-hidden full-width-dark-section' style={{ backgroundColor: FUNDS_COLORS.DARK_BLUE, minHeight: '500px', paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0', display: 'flex', alignItems: 'center' }}>
        <div className="container-fluid text-white" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
          <div className='row'>
            <div className='col-lg-6 my-3'>
              <h2 className='h2 text-start mb-4 mobile-header' style={{ marginTop: '0' }}>Moolah Capital GenAI Funds</h2>
              <p className='body-text text-start'>Our proprietary AI platform ingests vast amounts of blockchain, market, and sentiment data, using advanced algorithms and LLM reasoning to:</p>
              <ul className='body-text text-start'>
               <li><span style={{ fontWeight: 'bold' }}>Identify </span> emerging opportunities across sectors, protocols, and tokens.</li>
               <li><span style={{ fontWeight: 'bold' }}>Optimise</span> asset selection and weighting based on volatility, liquidity, and momentum factors.</li>
               <li><span style={{ fontWeight: 'bold' }}>Generate</span>  bespoke strategies that align with investor-defined risk tolerance, time horizon, and thematic preferences.</li>
               <li><span style={{ fontWeight: 'bold' }}>Design </span> their own AI-powered portfolios by setting goals and constraints.</li>
               <li><span style={{ fontWeight: 'bold' }}>Replicate</span> top-performing AI strategies created by other investors.</li>
              </ul>
              <p className='body-text text-start'>By blending the personalisation of self-directed investing with the rigour of institutional-grade AI analytics, the <strong>Moolah Capital AlphaGlobal GenAI LLM Funds</strong> Funds give investors access to a flexible, constantly evolving investment toolkit — one that can capture opportunities beyond the reach of traditional portfolio management methods.</p>
            </div>
            <div className='col-lg-6 my-3 d-flex justify-content-center align-items-center' style={{ padding: '20px' }}>
              <div className="fund-image-container" style={{ width: '100%', maxWidth: '600px' }}>
                <img
                  src={'/funds-smartbeta.jpg'}
                  alt='passive funds'
                  className='fund-image'
                  style={{ width: '100%', height: 'auto', minHeight: '450px', objectFit: 'cover', borderRadius: '12px' }}
                  onError={(e) => {
                    e.target.onerror = null; // Prevent infinite loop
                    e.target.src = "/placeholder.jpg";
                  }}
                />
              </div>
            </div>
          </div>
          {/* CTA Section - Full Width */}
          <div className="d-flex justify-content-center align-items-center my-4 text-center-mbl" style={{ minHeight: '150px', width: '100%' }}>
            <div className="d-flex flex-row gap-4 justify-content-center" style={{ width: '100%' }}>
              <CTASection
                title="Start Your Financial Journey"
                cta="Sign up"
                link="/signup#account-signup"
                theme='signup'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-dark-section"
              />
              <CTASection
                title="Request Info about this Fund"
                cta="Learn More"
                link="/learn?topic=smartbeta"
                theme='learn'
                style={{ width: '45%', minWidth: '250px' }}
                className="cta-dark-section"
              />
            </div>
          </div>
        </div>
      </section>

     
      {/* Section 7 - Investment Process */}
      <section id="investment-process" className='position-relative text-black overflow-hidden' style={{ backgroundColor: FUNDS_COLORS.WHITE, minHeight: '500px', padding: '60px 20px', display: 'flex', alignItems: 'center' }}>
        <div className='container'>
          <div className='row'>
            <div className='col-12 mb-4'>
              <h2 className='h2 text-center mb-4 mobile-header'>Our Investment Process</h2>
              <p className='body-text text-center mb-5'>We follow a disciplined, research-driven approach to portfolio construction</p>
            </div>
          </div>
          <div className='row g-4'>
            <div className='col-md-3 mb-4'>
              <div className='card h-100' style={{
                borderRadius: '16px',
                padding: '24px',
                backgroundColor: 'var(--card-color)',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                height: '100%',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                border: 'none',
                color: 'var(--text-white)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)';
              }}>
                <div className='card-body d-flex flex-column justify-content-between text-center'>
                  <div>
                    <h3 className='h4 mb-3 fw-bold' style={{ color: 'var(--text-white)' }}>1. Research</h3>
                    <p className='card-text small' style={{ color: 'var(--text-white)' }}>Comprehensive market analysis and fundamental research on crypto assets</p>
                  </div>
                </div>
              </div>
            </div>
            <div className='col-md-3 mb-4'>
              <div className='card h-100' style={{
                borderRadius: '16px',
                padding: '24px',
                backgroundColor: 'var(--card-color)',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                height: '100%',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                border: 'none',
                color: 'var(--text-white)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)';
              }}>
                <div className='card-body d-flex flex-column justify-content-between text-center'>
                  <div>
                    <h3 className='h4 mb-3 fw-bold' style={{ color: 'var(--text-white)' }}>2. Selection</h3>
                    <p className='card-text small' style={{ color: 'var(--text-white)' }}>Rigorous screening process to identify assets that meet our investment criteria</p>
                  </div>
                </div>
              </div>
            </div>
            <div className='col-md-3 mb-4'>
              <div className='card h-100' style={{
                borderRadius: '16px',
                padding: '24px',
                backgroundColor: 'var(--card-color)',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                height: '100%',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                border: 'none',
                color: 'var(--text-white)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)';
              }}>
                <div className='card-body d-flex flex-column justify-content-between text-center'>
                  <div>
                    <h3 className='h4 mb-3 fw-bold' style={{ color: 'var(--text-white)' }}>3. Construction</h3>
                    <p className='card-text small' style={{ color: 'var(--text-white)' }}>Strategic portfolio building with optimal weightings and risk management</p>
                  </div>
                </div>
              </div>
            </div>
            <div className='col-md-3 mb-4'>
              <div className='card h-100' style={{
                borderRadius: '16px',
                padding: '24px',
                backgroundColor: 'var(--card-color)',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                height: '100%',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                border: 'none',
                color: 'var(--text-white)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)';
              }}>
                <div className='card-body d-flex flex-column justify-content-between text-center'>
                  <div>
                    <h3 className='h4 mb-3 fw-bold' style={{ color: 'var(--text-white)' }}>4. Monitoring</h3>
                    <p className='card-text small' style={{ color: 'var(--text-white)' }}>Continuous oversight and periodic rebalancing to maintain optimal exposure</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 8 - Final CTA */}
      <SectionFrame shape="squares" />
      <section id="funds-cta" className="full-width-dark-section" style={{ backgroundColor: FUNDS_COLORS.DARK_BLUE, paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0' }}>
        <div className="container text-white" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
          <CTASection
            title="Take your first step towards your Financial Future"
            cta="Sign up"
            link="/signup#account-signup"
            theme="signup"
            titleStyle={{ color: 'var(--text-white)' }}
          />
        </div>
      </section>
      <SectionFrame2 shape="dots" />
      </div>
    </div>
  );
};

export default Funds;
