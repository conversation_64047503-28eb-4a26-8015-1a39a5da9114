import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import PanelCard from './PanelCard.jsx';
import SummaryBars from './SummaryBars.jsx';
import FundChart from './FundChart.jsx';
import PortfolioChart from './PortfolioChart.jsx';
import styles from './Homepage.module.css';
import CTASection from '../../App/CTA/CTASection.jsx';
import SectionFrame from '../Common/SectionFrame';
import SectionFrame2 from '../Common/SectionFrame2';

// Import global colors
import { COLORS } from '../../constants/colors';

// Use global colors directly
const HOMEPAGE_COLORS = COLORS;

const Homepage = () => {
  const [imgId, setImgId] = useState(0);
  const [isOneImageHover, setIsOneImageHover] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const videoRef = useRef(null);

  const handleMouseEnter = useCallback((imageId) => {
    setImgId(imageId);
    setIsOneImageHover(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setImgId(0);
    setIsOneImageHover(false);
  }, []);

  const scrollToTop = useCallback(() => window.scrollTo(0, 0), []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) {
      console.error("Video ref is null");
      return;
    }

    //console.log("Video element exists:", video);

    const handleCanPlay = () => {
      console.log("Video can play now");
      // Set playback rate to exactly 0.30 (30% of normal rate)
      video.playbackRate = 0.30;
      console.log("Setting video playback rate to 0.30");

      video.play().catch((err) => {
        console.warn('Autoplay blocked:', err);
      });
      setVideoLoaded(true);
    };

    const handleError = (e) => {
      console.error('Video failed to load:', e);
      setVideoLoaded(true); // Still mark as loaded to remove loading state
    };

    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('error', handleError);

    // Try to manually trigger play and set playback rate
    setTimeout(() => {
      if (video && !videoLoaded) {
        console.log("Attempting manual play");
        // Ensure playback rate is set even if canplay event hasn't fired
        video.playbackRate = 0.30;
        console.log("Setting video playback rate to 0.30 (manual)");

        video.play().catch(err => console.warn("Manual play failed:", err));
      }
    }, 1000);

    return () => {
      if (video) {
        video.removeEventListener('canplay', handleCanPlay);
        video.removeEventListener('error', handleError);
      }
    };
  }, [videoLoaded]);

  // Add a separate effect to handle metadata loading
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleMetadataLoaded = () => {
      // Set playback rate when metadata is loaded
      video.playbackRate = 0.30;
      console.log("Metadata loaded - setting video playback rate to 0.30");
    };

    video.addEventListener('loadedmetadata', handleMetadataLoaded);

    return () => {
      video.removeEventListener('loadedmetadata', handleMetadataLoaded);
    };
  }, []);

  // Fund data for charts
  const fundData = [
    {
      name: "AlphaGlobal Market Index Fund",
      performance: 85,
      risk: 30,
      fees: 95,
      liquidity: 90
    },
    {
      name: "AlphaGlobal Momentum Fund",
      performance: 78,
      risk: 65,
      fees: 70,
      liquidity: 85
    },
    {
      name: "AlphaGlobal DeFi Leaders Fund",
      performance: 82,
      risk: 45,
      fees: 80,
      liquidity: 88
    },
    {
      name: "AlphaGlobal Yield Fund",
      performance: 87,
      risk: 47,
      fees: 75,
      liquidity: 83
    },
    {
      name: "AlphaGlobal GenAI Fund",
      performance: 85,
      risk: 48,
      fees: 76,
      liquidity: 85
    },
    {
      name: "Moolah Capital Portfolio",
      performance: 90,
      risk: 48,
      fees: 74,
      liquidity: 82
    }
  ];

  // Fund data for charts
  const portfolioData = [
    {
      name: "Example of Portfolio Allocation",
      data: {
        'AlphaGlobal Momentum Fund': 5000,
        'AlphaGlobal GenAI BTC ETH Fund': 1500,
        'AlphaGlobal DeFI Leaders Fund': 1500,
        'AlphaGlobal GenAI CopyTraders Fund': 2000
      },
      type: 'portfolio' // Flag to tell the chart this is portfolio data
    }
  ];

  const panels = useMemo(() => [
    {
      title: 'Passive Index',
      desc: `<p>The passive fund follow a diversified mix of crypto assets or a broad market index to mirror overall market performance.</p>
             <p>It uses clear, rules-based methods, like market capitalization andtrading activity, to give low-cost exposure to the growth of the crypto market while spreading risk.</p>
             <p>Our research team reviews and adjusts the portfolio each month to keep it aligned with market trends and investment goals.</p>`,
      image: null,
      backgroundColor: 'var(--card-color)',      // Use card color
      link: '/funds#passive',
      infoLink: '/learn?topic=passive'
    },
    {
      title: 'Managed Funds',
      desc: `<p>Smart beta funds use rules-based strategies to select and weight crypto assets based on factors like momentum or volatility, aiming to outperform the market.</p>
             <p>Special Situations funds focus on unique events — such as market dislocations or catalysts — that can create short-term opportunities.</p>
             <p>By diversifying across strategies and managers, these funds seek to capture targeted gains while managing risk.</p>`,
      image: null,
      backgroundColor: 'var(--card-color)',      // Use card color
      link: '/funds#smartbeta',
      infoLink: '/learn?topic=smartbeta'
    },
    {
      title: 'GenAI',
      desc: `<p>GenAI funds are built by algorithms powered by generative AI and large language models (LLMs) such as GPT</p>
             <p>Unlike passive index or actively managed funds, they are not ready-made. Each fund is created based on an investor’s chosen goals, risk level, and asset preferences.</p>
             <p>The AI processes market data, news, and blockchain activity to design the strategy, then automatically monitors and adjusts it as conditions change.</p>`,
      image: null,
      backgroundColor: 'var(--card-color)',      // Use card color
      link: '/funds#special',
      infoLink: '/learn?topic=special'
    }
  ], []);

  const sections = useMemo(() => [
    {
      id: "moolah-capital",
      key: "moolah-capital",
      title: '<span style="font-size: 1.5em;">We build a select range of globally diversified crypto funds on passive index strategies, smart beta, yield, and special situations.</span>',
      description: [
        "Moolah Capital is a quantitative asset manager specialised in crypto asset investments. We develop proprietary fintech to enhance and automate how our financial services and investment strategies are delivered.",
      ],
      image: null,
      link: "/funds",
      linkText: "Explore",
      reverse: false,
      backgroundColor: HOMEPAGE_COLORS.MEDIUM_BLUE
    },
    {
      id: 'moolah-easysafe',
      key: 'moolah-easysafe',
      title: 'Market Index Fund',
      description: [
        "<strong>Moolah Capital AlphaGlobal Market Index Fund</strong> is a passive index strategy focused on the crypto and DeFi sectors. It tracks a diversified basket of leading digital assets, offering investors broad exposure to the market’s growth while minimising the need for active management.",
      ],
      image: '/home-easy-safe.jpg',
      chartData: fundData[0], // AlphaGlobal Market Index Fund
      backgroundColor: HOMEPAGE_COLORS.DARK_BLUE, // Darker blue background
      link: "/funds/capital-fund",
      linkText: "Explore",
      reverse: true
    },
    {
      id: 'moolah-easysafe',
      key: 'moolah-easysafe',
      title: 'GenAI Funds',
      description: [
        "<strong>Moolah Capital AlphaGlobal GenAI Funds</strong> enable investors to build personalised digital asset portfolios using generative AI  (GenAI) and large language models (LLMs). By defining their objectives, risk profile, and preferred sectors, investors can create strategies that are automatically managed with advanced, data-driven execution.",
      ],
      chartData: fundData[4], // AlphaGlobal DeFi Leaders Fund for GenAI
      backgroundColor: HOMEPAGE_COLORS.MEDIUM_BLUE, // Medium blue background
      link: "/funds/smartbeta-fund",
      linkText: "Explore",
      reverse: false
    },
    {
      id: 'moolah-hedge',
      key: 'moolah-hedge',
      title: 'Momentum Fund',
      description: [
        "<strong>Moolah Capital AlphaGlobal Momentum Fund</strong> is an active strategy that invests in digital and DeFi assets with strong upward trends. By following market momentum and adjusts positions as conditions change, it aims to capture growth opportunities while managing downside risk.",
      ],
      image: '/home-smart-simple.jpg',
      chartData: fundData[1], // AlphaGlobal Momentum Fund
      backgroundColor: HOMEPAGE_COLORS.DARK_BLUE, // Dark blue background
      link: "/funds/momentum-fund",
      linkText: "Explore",
      reverse: true
    },
    {
      id: 'moolah-easysafe',
      key: 'moolah-easysafe',
      title: 'DeFi Leaders Fund',
      description: [
        "<strong>Moolah Capital AlphaGlobal DeFi Leaders Fund</strong> focuses exclusively on the rapidly evolving world of decentralised finance (DeFi). It invests in leading DeFi projects and protocols, giving investors targeted exposure to this high-growth sector of the digital asset market.",
      ],
      image: '/home-easy-safe.jpg',
      chartData: fundData[2], // AlphaGlobal Yield Fund (shifted since GenAI took fundData[2])
      backgroundColor: HOMEPAGE_COLORS.MEDIUM_BLUE, // Medium blue background
      link: "/funds/defi-fund",
      linkText: "Explore",
      reverse: false
    },
    {
      id: 'moolah-hedge',
      key: 'moolah-hedge',
      title: 'Yield Fund',
      description: [
        "<strong>Moolah Capital AlphaGlobal Yield Fund</strong> is designed to generate regular returns from digital and DeFi markets. It invests in opportunities such as staking, yield farming, and lending protocols, aiming to provide a steady income stream while preserving capital.",
      ],
      image: '/home-smart-simple.jpg',
      chartData: fundData[3], // AlphaGlobal Market Index Fund (reusing since we need 4 charts)
      backgroundColor: HOMEPAGE_COLORS.DARK_BLUE, // Dark blue background
      link: "/funds/income-fund",
      linkText: "Explore",
      reverse: true
    },
    {
      id: 'moolah-easysafe',
      key: 'moolah-easysafe',
      title: 'Diversified Portfolio',
      description: [
        "Combining investments across multiple <strong>Moolah Capital AlphaGlobal</strong>  funds — Market Index, Momentum, DeFi Leaders, Special Situations, Yield, and GenAI LLM portfolios — builds a more diversified allocation. This blend spreads exposure across market beta, factor trends, DeFi income, event-driven opportunities, and AI-designed strategies, aiming to smooth returns and reduce reliance on any single driver.",
      ],
      image: '/home-easy-safe.jpg',
      chartData: fundData[5], // AlphaGlobal Yield Fund (shifted since GenAI took fundData[2])
      backgroundColor: HOMEPAGE_COLORS.MEDIUM_BLUE, // Medium blue background
      link: "/funds",
      linkText: "Explore",
      reverse: false
    },
    {
      id: "moolah-capital",
      key: "moolah-capital",
      title: 'Diversifying with a Mix-and-Match Strategy',
      description: [
        "An investor allocates $5,000 to the AlphaGlobal Momentum Fund, gaining exposure to assets with strong upward trends, and $1,500 to the AlphaGlobal DeFi Leaders Fund. They also invest $3,500 across two AI-designed portfolios — one they created themselves by setting their own objectives, and another that mirrors the strategy of a top-performing investor. This approach combines the discipline of a professionally managed fund with the flexibility and personalisation of AI-driven strategies. This is just one example of the many ways investors can combine our funds to suit their goals.",
      ],
      image: '/2 round graphs.jpg',
      chartData: portfolioData[0], // AlphaGlobal Yield Fund (shifted since GenAI took fundData[2])
      backgroundColor: HOMEPAGE_COLORS.DARK_BLUE, // Dark blue background
      link: "/genai",
      linkText: "Explore",
      reverse: true
    }

  ], []);

  const reasons = useMemo(() => [
    {
      id: 1,
      title: "A Smarter Approach to Digital Finance",
      description: "Moolah Capital delivers institutional-grade investment strategies tailored to the digital asset space—bridging traditional discipline with crypto opportunity."
    },
    {
      id: 2,
      title: "Built Around Your Goals",
      description: "We tailor portfolios to meet your investment objectives, actively managing risk, responding to market conditions, and ensuring full compliance."
    },
    {
      id: 3,
      title: "More Time, Less Complexity",
      description: "We handle the operational complexity—performance, compliance, and rebalancing—so you can focus on your own personal goals."
    },
    {
      id: 4,
      title: "Powered by Expert Research",
      description: "Our in-house research team fuels every investment decision, identifying high-conviction opportunities with disciplined risk management."
    },
    {
      id: 5,
      title: "Lower Costs, Lower Risk",
      description: "Avoid the overhead of building your own investment function. You retain full ownership, while we deliver performance, compliance, and peace of mind."
    },
    {
      id: 6,
      title: "Fast, Responsive and Transparent",
      description: "We respond quickly to market shifts and keep you fully informed—clear communication, consistent reporting, no surprises."
    }
  ], []);

  return (
    <div className="homepage_main position-relative" style={{ 
      marginTop: '0', 
      paddingTop: '0',
      backgroundColor: 'transparent' 
    }}>
      {/* Video Section with consolidated styles */}
      <section className="video-container" style={{ 
        position: 'relative', 
        width: '100%', 
        height: '100vh', 
        zIndex: 0,
        backgroundColor: 'transparent',
        marginTop: '0',
        paddingTop: '0',
        overflow: 'hidden'
      }}>
        {/* Loading placeholder */}
        {!videoLoaded && (
          <div
            className="video-placeholder"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundImage: 'url(/loading-home.webp)',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              zIndex: 1
            }}
          >
            <div className="spinner-border text-light" role="status">
              <span className="visually-hidden">Loading video...</span>
            </div>
          </div>
        )}

        {/* Video element */}
        <video
          id="homepage-video"
          autoPlay
          className="background-video"
          loop
          muted
          playsInline
          preload="auto"
          ref={videoRef}
          src="/moolah-home.mp4"
          style={{ 
            visibility: videoLoaded ? 'visible' : 'hidden',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            zIndex: 0
          }}
        />

        {/* Content overlay */}
        <div className="video-overlay-content" style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 2,
          textAlign: 'center',
          color: 'white'
        }}>
          <h1 className="display-4">Simple and Successful</h1>
          <h1 className="display-4">Crypto Investing</h1>
        </div>

        {/* Scroll indicator */}
        <div className="scroll-indicator" style={{
          position: 'absolute',
          bottom: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 2,
          textAlign: 'center',
          color: 'white'
        }}>
          <i className="bi bi-chevron-double-down"></i>
          <span>Scroll Down</span>
        </div>
      </section>

      {/* Content Container - Starts after full viewport */}
      <div className="page-content-container" style={{
        marginTop: '0',
        position: 'relative',
        zIndex: 2,
        backgroundColor: '#fff'
      }}>
        <section id='home-sections'>
      {sections.map((section, index) => (
        <section
          key={section.id}
          className={`${(section.backgroundColor === HOMEPAGE_COLORS.DARK_BLUE || section.backgroundColor === '#1A365D') ? 'full-width-dark-section' : (section.backgroundColor === HOMEPAGE_COLORS.MEDIUM_BLUE) ? 'full-width-medium-section' : ''}`}
          id={section.id}
          style={{
            backgroundColor: section.backgroundColor || (index % 2 === 0 ? '#ffffff' : '#1A365D'),
            minHeight: '500px',
            paddingTop: '60px',
            paddingBottom: '60px',
            paddingLeft: (section.backgroundColor === HOMEPAGE_COLORS.DARK_BLUE || section.backgroundColor === HOMEPAGE_COLORS.MEDIUM_BLUE || section.backgroundColor === '#1A365D') ? '0' : '20px',
            paddingRight: (section.backgroundColor === HOMEPAGE_COLORS.DARK_BLUE || section.backgroundColor === HOMEPAGE_COLORS.MEDIUM_BLUE || section.backgroundColor === '#1A365D') ? '0' : '20px',
            display: 'flex',
            alignItems: 'center'
          }}
        >
          <div className={`container ${(section.backgroundColor === '#1A365D' || section.backgroundColor === HOMEPAGE_COLORS.DARK_BLUE || (index % 2 !== 0 && !section.backgroundColor)) ? 'text-white' : (section.backgroundColor === HOMEPAGE_COLORS.MEDIUM_BLUE) ? 'text-black' : 'text-black'}`} style={{
            fontWeight: (section.backgroundColor === '#1A365D' || section.backgroundColor === HOMEPAGE_COLORS.MEDIUM_BLUE || section.backgroundColor === HOMEPAGE_COLORS.DARK_BLUE || (index % 2 !== 0 && !section.backgroundColor)) ? 'normal' : 'inherit',
            paddingLeft: (section.backgroundColor === HOMEPAGE_COLORS.DARK_BLUE || section.backgroundColor === HOMEPAGE_COLORS.MEDIUM_BLUE || section.backgroundColor === '#1A365D') ? '20px' : '15px',
            paddingRight: (section.backgroundColor === HOMEPAGE_COLORS.DARK_BLUE || section.backgroundColor === HOMEPAGE_COLORS.MEDIUM_BLUE || section.backgroundColor === '#1A365D') ? '20px' : '15px'
          }}>
            {typeof section.content === 'object' && React.isValidElement(section.content) ? (
              section.content
            ) : Array.isArray(section.content) ? (
              section.content
            ) : (
              <div className="row align-items-center">
                <div className={`col-lg-5 text-center text-center-mbl ${section.reverse ? 'order-lg-1' : 'order-lg-2'} order-1 mb-4 mb-lg-0`}>
                  {section.chartData ? (
                    <div style={{
                      backgroundColor: 'transparent',
                      maxWidth: '400px',
                      margin: '0 auto',
                      border: 'none',
                      borderRadius: '0.5rem',
                      boxShadow: 'none'
                    }}>
                      {section.chartData.type === 'portfolio' ? (
                        <PortfolioChart
                          portfolioData={section.chartData}
                          compact={false}
                          textColor={(section.backgroundColor === '#1A365D' || section.backgroundColor === HOMEPAGE_COLORS.DARK_BLUE) ? 'text-white' : (section.backgroundColor === HOMEPAGE_COLORS.MEDIUM_BLUE) ? 'text-dark' : 'text-dark'}
                          backgroundColor="transparent"
                        />
                      ) : (
                        <FundChart
                          fundData={section.chartData}
                          compact={false}
                          textColor={(section.backgroundColor === '#1A365D' || section.backgroundColor === HOMEPAGE_COLORS.DARK_BLUE) ? 'text-white' : (section.backgroundColor === HOMEPAGE_COLORS.MEDIUM_BLUE) ? 'text-dark' : 'text-dark'}
                          backgroundColor="transparent"
                        />
                      )}
                    </div>
                  ) : section.image ? (
                    <img
                      alt={section.title}
                      className="img-fluid ms-4 rounded-lg"
                      loading="lazy"
                      src={section.image}
                      style={{ maxWidth: '100%', maxHeight: '300px', objectFit: 'cover', width: 'auto' }}
                      onError={(e) => {
                        e.target.onerror = null; // Prevent infinite loop
                        e.target.src = "/placeholder.jpg";
                      }}
                    />
                  ) : null}
                </div>
                <div className={`col-lg-7 text-lg-start text-center ${section.reverse ? 'order-lg-2' : 'order-lg-1'} order-2`}>
                  <h2 className="h2 ms-4" dangerouslySetInnerHTML={{ __html: section.title }}></h2>
                  {typeof section.description === 'string' ? (
                    <p className="body-text ms-4 ms-lg-4" dangerouslySetInnerHTML={{ __html: section.description }}></p>
                  ) : Array.isArray(section.description) ? (
                    section.description.map((paragraph, idx) => (
                      <p key={idx} className="body-text ms-lg-4" dangerouslySetInnerHTML={{ __html: paragraph }}></p>
                    ))
                  ) : null}
                  {section.link && (
                    <div className="text-lg-start text-center">
                      <Link role="button" className="btn sec-4-button ms-lg-4 mt-4" onClick={scrollToTop} to={section.link}>
                        {section.linkText || "Explore"}
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </section>
      ))}
      </section>

      <section id="funds-cta1" className="full-width-dark-section" style={{ backgroundColor: HOMEPAGE_COLORS.DARK_BLUE, paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0' }}>
        <div className="container text-center-mbl text-white" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
          <CTASection
            title="Start Your Financial Journey"
            cta="Sign up"
            link="/signup#account-signup"
            theme="signup"
            titleStyle={{ color: 'white' }}
          />
        </div>
      </section>

      <section id="home-panels"
        className="body-content-py-margin body-content-px-margin"
        style={{ backgroundColor: '#ffffff' }}
        aria-labelledby="investment-options-heading"
      >
        <div className="container-fluid px-4 px-md-5 text-black">
          <h2 id="investment-options-heading" className="h2 text-start mb-5">Moolah Fund Options</h2>
          <div className="row g-5 justify-content-center" role="list">
            {panels.map((panel, index) => (
              <div className="col-md-10 col-lg-4" role="listitem" key={index}>
                <PanelCard
                  panel={panel}
                  index={index}
                  isHovered={imgId === index + 1 && isOneImageHover}
                  onMouseEnter={handleMouseEnter}
                  onMouseLeave={handleMouseLeave}
                  scrollToTop={scrollToTop}
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      <section id='home-why' className="full-width-dark-section" style={{ backgroundColor: HOMEPAGE_COLORS.DARK_BLUE, paddingTop: '120px', paddingBottom: '120px', paddingLeft: '0', paddingRight: '0' }}>
        <div className="container text-white" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
          <h2 className="h2 text-start mb-5">Why Choose Moolah Capital?</h2>
          <div className="row g-4">
            {reasons.map((reason) => (
              <div className="col-md-6 col-lg-4" key={reason.id}>
                <div className="card h-100 shadow-sm" style={{ backgroundColor: HOMEPAGE_COLORS.MEDIUM_BLUE, color: 'black' }}>
                  <div className="card-body d-flex flex-column" style={{ color: 'black' }}>
                    <h3 className="card-title h5 mb-3 fw-bold text-start" style={{ color: 'black' }}>{reason.title}</h3>
                    <p className="card-text text-start" style={{ color: 'black' }}>{reason.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Frame after Why Choose section */}
      <SectionFrame />

      <section id="home-cta" className="full-width-dark-section" style={{ backgroundColor: HOMEPAGE_COLORS.DARK_BLUE, paddingTop: '60px', paddingBottom: '60px', paddingLeft: '0', paddingRight: '0' }}>
        <div className="container text-center-mbl text-white" style={{ paddingLeft: '20px', paddingRight: '20px' }}>
          <CTASection
            title="Discover the Right Funds for Your Goals."
            cta="Click here to view Funds"
            link="/funds"
            theme="learn"
            titleStyle={{ color: 'white' }}
          />
        </div>
      </section>
      <SectionFrame2 />
      </div>
    </div>
  );
};

export default Homepage;
