import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styles from './PanelCard.module.css';
import FundChart from './FundChart.jsx';
import { COLORS } from '../../constants/colors';

const PanelCard = ({ panel, index, isHovered, onMouseEnter, onMouseLeave, scrollToTop }) => {
  const imageSrc = panel?.image || panel?.img || null;
  const usesSolidColor = Boolean(panel?.backgroundColor);
  const hasChartData = Boolean(panel?.chartData);
  const [backgroundImage, setBackgroundImage] = useState(imageSrc);

  // Check if the image exists (only if an image source is provided)
  useEffect(() => {
    if (!imageSrc) return;
    const img = new Image();
    img.src = imageSrc;

    img.onerror = () => {
      setBackgroundImage('/placeholder.jpg');
    };
  }, [imageSrc]);

  return (
    <div
      className={`${styles.panelCard} card h-100 text-white`}
      style={{
        backgroundImage: (usesSolidColor || hasChartData) ? 'none' : (backgroundImage ? `url(${backgroundImage})` : 'none'),
        backgroundSize: (usesSolidColor || hasChartData) ? undefined : 'cover',
        backgroundPosition: (usesSolidColor || hasChartData) ? undefined : 'center',
        backgroundBlendMode: (usesSolidColor || hasChartData) ? 'normal' : (isHovered ? 'overlay' : 'normal'),
        backgroundColor: (usesSolidColor || hasChartData)
          ? (isHovered ? `color-mix(in srgb, ${panel.backgroundColor || COLORS.CARD_COLOR} 80%, black 20%)` : (panel.backgroundColor || COLORS.CARD_COLOR))
          : (isHovered ? COLORS.BLACK_30 : 'transparent'),
        transition: 'background-color 0.3s ease, background-image 0.3s ease, transform 0.3s ease'
      }}
      onMouseEnter={() => onMouseEnter(index + 1)}
      onMouseLeave={onMouseLeave}
    >
      {hasChartData ? (
        <div className="d-flex flex-column h-100">
          <div className="p-3 pb-2">
            <h3 className="card-title h4 fw-bold">{panel.title}</h3>
            <div
              className={`${styles.panelDescription} card-text small`}
              dangerouslySetInnerHTML={{ __html: panel.desc }}
            />
          </div>
          <div className="flex-grow-1 px-3">
            <FundChart fundData={panel.chartData} compact={true} />
          </div>
          <div className="p-3 pt-0">
            <div className="mt-2 text-center d-flex justify-content-center">
              <Link
                to={panel.link}
                className={`btn ${styles.panelExploreButton}`}
                onClick={scrollToTop}
              >
                Explore
              </Link>
            </div>
          </div>
        </div>
      ) : (
        <div className="card-body d-flex flex-column justify-content-between">
          <div>
            <h3 className="card-title h4 fw-bold">{panel.title}</h3>
            <div
              className={`${styles.panelDescription} card-text small`}
              dangerouslySetInnerHTML={{ __html: panel.desc }}
            />
          </div>
          <div className="mt-4 text-center d-flex justify-content-center">
            <Link
              to={panel.link}
              className={`btn ${styles.panelExploreButton}`}
              onClick={scrollToTop}
            >
              Explore
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};
export default PanelCard;
