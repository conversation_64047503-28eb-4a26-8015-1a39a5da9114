import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useContent } from '../../context/ContentContext';
import { motion, AnimatePresence } from 'framer-motion';

const Navbar = ({ isScrolled }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const location = useLocation();

  const { content } = useContent();
  const { mainNav } = content.navigation;

  const isActive = (path) => location.pathname === path;

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.nav-item.dropdown')) {
        setActiveDropdown(null);
      }
    };
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [location.pathname]);

  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  const toggleDropdown = (index, event) => {
    event.preventDefault();
    setActiveDropdown(activeDropdown === index ? null : index);
  };

  const closeNavbar = () => {
    setIsOpen(false);
    setActiveDropdown(null);
  };

  const scrollToTop = () => {
    window.scrollTo(0, 0);
    closeNavbar();
  };

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsOpen(false);
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isOpen && !event.target.closest('.offcanvas') && !event.target.closest('.navbar-toggler')) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isOpen]);

  return (
    <div className="container-fluid px-0">
      <nav
        className="navbar navbar-expand-md lato-regular light py-2 bg-none"
        data-testid="navigation"
        style={{ 
          minHeight: '50px',  // Increased from 64px to 70px
          margin: 0, 
          padding: 0,
          display: 'flex',
          alignItems: 'center'  // Center items vertically
        }}
      >
        <div className="container-fluid px-0" style={{ display: 'flex', alignItems: 'center' }}>
          <Link 
            className="navbar-brand fs-3 text-light" 
            onClick={scrollToTop} 
            to="/"
            style={{ 
              paddingBottom: '8px',  // Add padding to lift text from bottom
              marginBottom: '4px'    // Add margin to lift text from bottom
            }}
          >
            Moolah Capital
          </Link>

          <button
            className="navbar-toggler d-md-none text-light"
            onClick={() => setIsOpen(true)}
            type="button"
            aria-label="Toggle navigation menu"
            style={{
              padding: '12px',
              minWidth: '48px',
              minHeight: '48px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: 'none',
              border: 'none',
              cursor: 'pointer'
            }}
          >
            <span className="bi bi-list text-light" style={{ fontSize: '24px' }} />
          </button>

          <div className="collapse gap-2 navbar-collapse d-md-flex" style={{ alignItems: 'center' }}>
            <ul className="navbar-nav ms-auto gap-4 mb-0 mb-lg-0">  {/* Changed mb-2 to mb-0 */}
              {mainNav.map((item, index) => (
                <li className={`nav-item pt-3 ${item.hasDropdown ? 'dropdown' : ''}`} key={item.path}>
                  {item.hasDropdown ? (
                    <div className="nav-link-container position-relative">
                      <Link
                        to={item.path}
                        className={`nav-link light ${isActive(item.path) ? 'active text-light' : 'unactive text-light'}`}
                        onClick={(e) => {
                          closeNavbar();
                          scrollToTop();
                        }}
                        style={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          position: 'relative',
                          textDecoration: 'none',
                          borderBottom: 'none'
                        }}
                      >
                        {item.name}
                      </Link>
                      <button
                        className="dropdown-toggle-btn"
                        onClick={(e) => {
                          e.preventDefault();
                          toggleDropdown(index, e);
                        }}
                        aria-label="Toggle dropdown menu"
                        style={{
                          background: 'none',
                          border: 'none',
                          position: 'absolute',
                          right: '-20px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          padding: '5px',
                          cursor: 'pointer'
                        }}
                      >
                        <span
                          className="dropdown-chevron text-white"
                          style={{
                            transition: 'transform 0.3s ease',
                            transform: activeDropdown === index ? 'rotate(90deg)' : 'rotate(0deg)'
                          }}
                        >
                          &raquo;
                        </span>
                      </button>
                      {activeDropdown === index && (
                        <div
                          className="dropdown-menu show"
                          style={{
                            backgroundColor: 'rgba(4, 10, 15, 0.95)',
                            border: 'none',
                            borderRadius: '8px',
                            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)',
                            padding: '8px 0',
                            minWidth: '200px',
                            backdropFilter: 'blur(10px)'
                          }}
                        >
                          {[...item.dropdownItems].sort((a, b) => a.name.localeCompare(b.name)).map((dropdownItem) => (
                            <Link
                              className={`dropdown-item ${isActive(dropdownItem.path) ? 'active-fund' : ''}`}
                              key={dropdownItem.path}
                              onClick={() => {
                                closeNavbar();
                                scrollToTop();
                              }}
                              to={dropdownItem.path}
                              style={{
                                color: 'var(--text-white)',
                                padding: '12px 20px',
                                fontSize: '0.95rem',
                                fontWeight: '400',
                                textDecoration: 'none',
                                transition: 'all 0.2s ease',
                                borderBottom: 'none',
                                backgroundColor: 'transparent'
                              }}
                              onMouseEnter={(e) => {
                                e.target.style.backgroundColor = 'rgba(0, 255, 200, 0.1)';
                                e.target.style.borderLeft = '3px solid var(--frame-color)';
                                e.target.style.paddingLeft = '17px';
                              }}
                              onMouseLeave={(e) => {
                                e.target.style.backgroundColor = 'transparent';
                                e.target.style.borderLeft = 'none';
                                e.target.style.paddingLeft = '20px';
                              }}
                            >
                              {dropdownItem.name}
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.path}
                      className={`nav-link light ${isActive(item.path) ? 'active text-light' : 'unactive text-light'}`}
                      onClick={() => {
                        closeNavbar();
                        scrollToTop();
                      }}
                      style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        position: 'relative',
                        textDecoration: 'none',
                        borderBottom: 'none'
                      }}
                    >
                      {item.name}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Offcanvas */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'tween', duration: 0.3 }}
            className="offcanvas show"
            style={{
              position: 'fixed',
              top: 0,
              right: 0,
              height: '100vh',
              width: '100vw',
              backgroundColor: 'rgba(139, 30, 45, 0.95)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              zIndex: 1050,
              overflowY: 'auto',
              paddingTop: '64px',
              display: isOpen ? 'block' : 'none' // Explicitly set display based on isOpen state
            }}
            tabIndex="-1"
          >
            <div className="offcanvas-header position-absolute top-0 w-100 d-flex justify-content-between align-items-center px-3 py-2">
              <Link
                className="h1 fs-4 text-light moolah-capital-link no-underline"
                onClick={scrollToTop}
                to="/"
                style={{ textDecoration: 'none', borderBottom: 'none' }}
              >
                Moolah Capital
              </Link>
              <button
                className="btn-close text-light"
                onClick={() => setIsOpen(false)}
                type="button"
                aria-label="Close mobile menu"
                style={{
                  background: 'none',
                  border: 'none',
                  padding: '10px',
                  cursor: 'pointer'
                }}
              >
                <i className="bi bi-x-lg text-white" style={{ fontSize: '24px' }}></i>
              </button>
            </div>
            <div className="offcanvas-body mt-5 pt-3">
              <ul className="navbar-nav ms-auto gap-3">
                {mainNav.map((item, index) => (
                  <li className={`nav-item mobile-nav-item ${item.hasDropdown ? 'dropdown' : ''}`} key={item.path}>
                    {item.hasDropdown ? (
                      <div className="mobile-nav-item-wrapper" style={{ width: '100%' }}>
                        <Link
                          className={`nav-link light mobile-nav-link ${isActive(item.path) ? 'active text-light' : 'unactive text-light'}`}
                          to={item.path}
                          onClick={() => {
                            closeNavbar();
                            scrollToTop();
                          }}
                          style={{
                            padding: '16px 12px',
                            fontSize: '18px',
                            fontWeight: '500',
                            textDecoration: 'none',
                            display: 'flex',
                            justifyContent: 'space-between',
                            width: '100%'
                          }}
                        >
                          <span>{item.name}</span>
                          <button
                            className="dropdown-toggle-btn"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              toggleDropdown(index, e);
                            }}
                            aria-label="Toggle dropdown menu"
                            style={{
                              background: 'none',
                              border: 'none',
                              padding: '5px',
                              cursor: 'pointer'
                            }}
                          >
                            <span
                              className="dropdown-chevron text-white"
                              style={{
                                transition: 'transform 0.3s ease',
                                transform: activeDropdown === index ? 'rotate(90deg)' : 'rotate(0deg)',
                                fontSize: '1.65rem'
                              }}
                            >
                              &raquo;
                            </span>
                          </button>
                        </Link>
                        {activeDropdown === index && (
                          <div className="dropdown-menu show" style={{ 
                            position: 'static',
                            width: '100%',
                            backgroundColor: 'rgba(255, 255, 255, 0.1)',
                            border: 'none',
                            borderRadius: 0,
                            marginTop: 0,
                            padding: 0
                          }}>
                            {[...item.dropdownItems].sort((a, b) => a.name.localeCompare(b.name)).map((dropdownItem) => (
                              <Link
                                className={`dropdown-item ${isActive(dropdownItem.path) ? 'active-fund' : ''}`}
                                key={dropdownItem.path}
                                onClick={() => {
                                  closeNavbar();
                                  scrollToTop();
                                }}
                                to={dropdownItem.path}
                                style={{
                                  color: 'white',
                                  padding: '14px 24px',
                                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
                                }}
                              >
                                {dropdownItem.name}
                              </Link>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      <Link
                        className={`nav-link light mobile-nav-link ${isActive(item.path) ? 'active text-light' : 'unactive text-light'}`}
                        to={item.path}
                        onClick={() => {
                          closeNavbar();
                          scrollToTop();
                        }}
                        style={{
                          padding: '16px 12px',
                          fontSize: '18px',
                          fontWeight: '500',
                          textDecoration: 'none',
                          position: 'relative'  // Add position relative for absolute positioning of underline
                        }}
                      >
                        {item.name}
                        {isActive(item.path) && (
                          <span className="custom-underline"></span>
                        )}
                      </Link>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Dark overlay */}
      {isOpen && (
        <div
          className="mobile-menu-overlay"
          onClick={() => setIsOpen(false)}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1040
          }}
        />
      )}
    </div>
  );
};

export default Navbar;
