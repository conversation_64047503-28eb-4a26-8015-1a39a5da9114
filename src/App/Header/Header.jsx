import React, { useState, useEffect } from 'react';
import Navbar from '../Navbar/Navbar.jsx';
import { useLocation } from 'react-router-dom';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);

  const pagesOnly = ['/', '/about', '/funds', '/faq', '/genai', '/strategies'];
  const location = useLocation();
  const isPageInPagesOnly = pagesOnly.includes(location.pathname);
  const isHomePage = location.pathname === '/';

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Force transparent background for home page
  const headerStyle = {
    position: 'fixed',
    top: '0',
    right: '0',
    zIndex: '1000',
    width: '100%',
    height: window.innerWidth <= 768 ? '70px' : 'auto',
    backgroundColor: isHomePage && !isScrolled ? 'transparent' :
                     isPageInPagesOnly && !isScrolled ? 'transparent' : 'var(--header-footer-red)',
    transition: 'background-color 0.3s ease',
    paddingTop: '8px',
    paddingBottom: '8px'
  };

  return (
    <div
      className={`p-3 navbar-wrapper ${isHomePage ? '' : (isPageInPagesOnly && !isScrolled ? '' : 'primary')}`}
      style={headerStyle}
      data-is-home={isHomePage ? 'true' : 'false'}
      data-is-scrolled={isScrolled ? 'true' : 'false'}
    >
      <Navbar isScrolled={isScrolled} />
    </div>
  );
}

export default Header;
