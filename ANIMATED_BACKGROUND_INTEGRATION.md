# Stripe-Style Animated Background Integration Guide

## What You've Got

I've created a **Stripe-inspired animated geometric background** component that creates floating, slowly moving shapes just like the ones you see in the top-left corner of <PERSON><PERSON>'s homepage.

## Files Created

1. **`src/Components/Common/AnimatedBackground.jsx`** - Main component
2. **`src/Components/Common/AnimatedBackground.css`** - Styling and optimizations  
3. **`src/Components/Common/AnimatedBackgroundDemo.jsx`** - Example usage

## How to Integrate

### Step 1: Import the Component

Add this import to any page where you want animated backgrounds:

```jsx
import AnimatedBackground from '../Common/AnimatedBackground';
```

### Step 2: Add to Your Sections

Here's how to add it to your existing sections:

#### For Hero Sections (Stripe-style corner animation):

```jsx
<section style={{ 
  position: 'relative', 
  height: '100vh', 
  backgroundColor: '#040a0f',
  overflow: 'hidden'
}}>
  {/* Stripe-style corner animation */}
  <AnimatedBackground
    density="medium"
    colors={['#00ffc8', '#635bff']}
    position="corner"
    speed="slow"
    shapes={['circle', 'square', 'diamond']}
  />
  
  {/* Your existing content */}
  <div style={{ position: 'relative', zIndex: 2 }}>
    <h1>Your Hero Content</h1>
  </div>
</section>
```

#### For CTA Sections:

```jsx
<section className="full-width-dark-section" style={{ 
  backgroundColor: '#040a0f', 
  position: 'relative',
  overflow: 'hidden'
}}>
  {/* Subtle full background animation */}
  <AnimatedBackground
    density="low"
    colors={['rgba(0, 255, 200, 0.1)', 'rgba(99, 91, 255, 0.1)']}
    position="full"
    speed="slow"
    shapes={['circle', 'square']}
  />
  
  {/* Your existing CTA content */}
  <div className="container" style={{ position: 'relative', zIndex: 2 }}>
    <CTASection ... />
  </div>
</section>
```

#### For Medium Blue Sections:

```jsx
<section style={{ 
  backgroundColor: '#b8e6ff', 
  position: 'relative',
  overflow: 'hidden'
}}>
  {/* Center-focused animation with dark shapes */}
  <AnimatedBackground
    density="medium"
    colors={['rgba(4, 10, 15, 0.1)', 'rgba(4, 10, 15, 0.15)']}
    position="center"
    speed="medium"
    shapes={['triangle', 'circle']}
  />
  
  {/* Your content */}
  <div className="container" style={{ position: 'relative', zIndex: 2 }}>
    Your content here
  </div>
</section>
```

## Configuration Options

### `density`
- `"low"` - 8 shapes (subtle)
- `"medium"` - 12 shapes (balanced) 
- `"high"` - 18 shapes (prominent)

### `position`
- `"corner"` - Top-left corner like Stripe (40% width, 60% height)
- `"center"` - Center area (60% width, 60% height)
- `"full"` - Full section coverage

### `speed`
- `"slow"` - 8-12 second animations (recommended)
- `"medium"` - 5-8 second animations
- `"fast"` - 3-5 second animations

### `shapes`
Array of: `['circle', 'square', 'triangle', 'diamond']`

### `colors`
Array of colors, e.g., `['#00ffc8', '#635bff']`

## Quick Integration Examples

### Homepage Hero Section
```jsx
// Add to your video section
<section className="video-container" style={{ position: 'relative', overflow: 'hidden' }}>
  <AnimatedBackground
    density="medium"
    colors={['#00ffc8', '#635bff']}
    position="corner"
    speed="slow"
    shapes={['circle', 'square']}
  />
  {/* Your existing video and content */}
</section>
```

### Dark Blue CTA Sections
```jsx
<section id="home-cta" className="full-width-dark-section" style={{ position: 'relative', overflow: 'hidden' }}>
  <AnimatedBackground
    density="low"
    colors={['rgba(0, 255, 200, 0.1)', 'rgba(99, 91, 255, 0.1)']}
    position="full"
    speed="slow"
  />
  <div className="container" style={{ position: 'relative', zIndex: 2 }}>
    {/* Your existing CTA content */}
  </div>
</section>
```

## Important Styling Requirements

1. **Parent section must have:**
   - `position: 'relative'`
   - `overflow: 'hidden'`

2. **Content must have:**
   - `position: 'relative'`
   - `zIndex: 2` (to appear above animation)

3. **AnimatedBackground automatically gets:**
   - `position: 'absolute'`
   - `zIndex: -1`

## Performance Features

✅ **Mobile Optimized** - Fewer shapes on smaller screens  
✅ **Accessibility** - Respects `prefers-reduced-motion`  
✅ **GPU Accelerated** - Uses `transform` for smooth animations  
✅ **Responsive** - Adapts to all screen sizes  

## Test It Out

1. Run your development server
2. Check `src/Components/Common/AnimatedBackgroundDemo.jsx` for live examples
3. Add to your existing sections following the patterns above

The animations will be subtle, professional, and exactly like Stripe's - floating geometric shapes that add visual interest without being distracting!
